"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var pollingRecorderSource_exports = {};
__export(pollingRecorderSource_exports, {
  source: () => source
});
module.exports = __toCommonJS(pollingRecorderSource_exports);
const source = '\nvar __commonJS = obj => {\n  let required = false;\n  let result;\n  return function __require() {\n    if (!required) {\n      required = true;\n      let fn;\n      for (const name in obj) { fn = obj[name]; break; }\n      const module = { exports: {} };\n      fn(module.exports, module);\n      result = module.exports;\n    }\n    return result;\n  }\n};\nvar __export = (target, all) => {for (var name in all) target[name] = all[name];};\nvar __toESM = mod => ({ ...mod, \'default\': mod });\nvar __toCommonJS = mod => ({ ...mod, __esModule: true });\n\n\n// packages/injected/src/recorder/pollingRecorder.ts\nvar pollingRecorder_exports = {};\n__export(pollingRecorder_exports, {\n  PollingRecorder: () => PollingRecorder,\n  default: () => pollingRecorder_default\n});\nmodule.exports = __toCommonJS(pollingRecorder_exports);\n\n// packages/injected/src/recorder/clipPaths.ts\nvar svgJson = { "tagName": "svg", "children": [{ "tagName": "defs", "children": [{ "tagName": "clipPath", "attrs": { "width": "16", "height": "16", "viewBox": "0 0 16 16", "fill": "currentColor", "id": "icon-gripper" }, "children": [{ "tagName": "path", "attrs": { "d": "M5 3h2v2H5zm0 4h2v2H5zm0 4h2v2H5zm4-8h2v2H9zm0 4h2v2H9zm0 4h2v2H9z" } }] }, { "tagName": "clipPath", "attrs": { "width": "16", "height": "16", "viewBox": "0 0 16 16", "fill": "currentColor", "id": "icon-circle-large-filled" }, "children": [{ "tagName": "path", "attrs": { "d": "M8 1a6.8 6.8 0 0 1 1.86.253 6.899 6.899 0 0 1 3.083 1.805 6.903 6.903 0 0 1 1.804 3.083C14.916 6.738 15 7.357 15 8s-.084 1.262-.253 1.86a6.9 6.9 0 0 1-.704 1.674 7.157 7.157 0 0 1-2.516 2.509 6.966 6.966 0 0 1-1.668.71A6.984 6.984 0 0 1 8 15a6.984 6.984 0 0 1-1.86-.246 7.098 7.098 0 0 1-1.674-.711 7.3 7.3 0 0 1-1.415-1.094 7.295 7.295 0 0 1-1.094-1.415 7.098 7.098 0 0 1-.71-1.675A6.985 6.985 0 0 1 1 8c0-.643.082-1.262.246-1.86a6.968 6.968 0 0 1 .711-1.667 7.156 7.156 0 0 1 2.509-2.516 6.895 6.895 0 0 1 1.675-.704A6.808 6.808 0 0 1 8 1z" } }] }, { "tagName": "clipPath", "attrs": { "width": "16", "height": "16", "viewBox": "0 0 16 16", "fill": "currentColor", "id": "icon-inspect" }, "children": [{ "tagName": "path", "attrs": { "fill-rule": "evenodd", "clip-rule": "evenodd", "d": "M1 3l1-1h12l1 1v6h-1V3H2v8h5v1H2l-1-1V3zm14.707 9.707L9 6v9.414l2.707-2.707h4zM10 13V8.414l3.293 3.293h-2L10 13z" } }] }, { "tagName": "clipPath", "attrs": { "width": "16", "height": "16", "viewBox": "0 0 16 16", "fill": "currentColor", "id": "icon-whole-word" }, "children": [{ "tagName": "path", "attrs": { "fill-rule": "evenodd", "clip-rule": "evenodd", "d": "M0 11H1V13H15V11H16V14H15H1H0V11Z" } }, { "tagName": "path", "attrs": { "d": "M6.84048 11H5.95963V10.1406H5.93814C5.555 10.7995 4.99104 11.1289 4.24625 11.1289C3.69839 11.1289 3.26871 10.9839 2.95718 10.6938C2.64924 10.4038 2.49527 10.0189 2.49527 9.53906C2.49527 8.51139 3.10041 7.91341 4.3107 7.74512L5.95963 7.51416C5.95963 6.57959 5.58186 6.1123 4.82632 6.1123C4.16389 6.1123 3.56591 6.33789 3.03238 6.78906V5.88672C3.57307 5.54297 4.19612 5.37109 4.90152 5.37109C6.19416 5.37109 6.84048 6.05501 6.84048 7.42285V11ZM5.95963 8.21777L4.63297 8.40039C4.22476 8.45768 3.91682 8.55973 3.70914 8.70654C3.50145 8.84977 3.39761 9.10579 3.39761 9.47461C3.39761 9.74316 3.4925 9.96338 3.68228 10.1353C3.87564 10.3035 4.13166 10.3877 4.45035 10.3877C4.8872 10.3877 5.24706 10.2355 5.52994 9.93115C5.8164 9.62321 5.95963 9.2347 5.95963 8.76562V8.21777Z" } }, { "tagName": "path", "attrs": { "d": "M9.3475 10.2051H9.32601V11H8.44515V2.85742H9.32601V6.4668H9.3475C9.78076 5.73633 10.4146 5.37109 11.2489 5.37109C11.9543 5.37109 12.5057 5.61816 12.9032 6.1123C13.3042 6.60286 13.5047 7.26172 13.5047 8.08887C13.5047 9.00911 13.2809 9.74674 12.8333 10.3018C12.3857 10.8532 11.7734 11.1289 10.9964 11.1289C10.2695 11.1289 9.71989 10.821 9.3475 10.2051ZM9.32601 7.98682V8.75488C9.32601 9.20964 9.47282 9.59635 9.76644 9.91504C10.0636 10.2301 10.4396 10.3877 10.8944 10.3877C11.4279 10.3877 11.8451 10.1836 12.1458 9.77539C12.4502 9.36719 12.6024 8.79964 12.6024 8.07275C12.6024 7.46045 12.4609 6.98063 12.1781 6.6333C11.8952 6.28597 11.512 6.1123 11.0286 6.1123C10.5166 6.1123 10.1048 6.29134 9.7933 6.64941C9.48177 7.00391 9.32601 7.44971 9.32601 7.98682Z" } }] }, { "tagName": "clipPath", "attrs": { "width": "16", "height": "16", "viewBox": "0 0 16 16", "fill": "currentColor", "id": "icon-eye" }, "children": [{ "tagName": "path", "attrs": { "d": "M7.99993 6.00316C9.47266 6.00316 10.6666 7.19708 10.6666 8.66981C10.6666 10.1426 9.47266 11.3365 7.99993 11.3365C6.52715 11.3365 5.33324 10.1426 5.33324 8.66981C5.33324 7.19708 6.52715 6.00316 7.99993 6.00316ZM7.99993 7.00315C7.07946 7.00315 6.33324 7.74935 6.33324 8.66981C6.33324 9.59028 7.07946 10.3365 7.99993 10.3365C8.9204 10.3365 9.6666 9.59028 9.6666 8.66981C9.6666 7.74935 8.9204 7.00315 7.99993 7.00315ZM7.99993 3.66675C11.0756 3.66675 13.7307 5.76675 14.4673 8.70968C14.5344 8.97755 14.3716 9.24908 14.1037 9.31615C13.8358 9.38315 13.5643 9.22041 13.4973 8.95248C12.8713 6.45205 10.6141 4.66675 7.99993 4.66675C5.38454 4.66675 3.12664 6.45359 2.50182 8.95555C2.43491 9.22341 2.16348 9.38635 1.89557 9.31948C1.62766 9.25255 1.46471 8.98115 1.53162 8.71321C2.26701 5.76856 4.9229 3.66675 7.99993 3.66675Z" } }] }, { "tagName": "clipPath", "attrs": { "width": "16", "height": "16", "viewBox": "0 0 16 16", "fill": "currentColor", "id": "icon-symbol-constant" }, "children": [{ "tagName": "path", "attrs": { "fill-rule": "evenodd", "clip-rule": "evenodd", "d": "M4 6h8v1H4V6zm8 3H4v1h8V9z" } }, { "tagName": "path", "attrs": { "fill-rule": "evenodd", "clip-rule": "evenodd", "d": "M1 4l1-1h12l1 1v8l-1 1H2l-1-1V4zm1 0v8h12V4H2z" } }] }, { "tagName": "clipPath", "attrs": { "width": "16", "height": "16", "viewBox": "0 0 16 16", "fill": "currentColor", "id": "icon-check" }, "children": [{ "tagName": "path", "attrs": { "fill-rule": "evenodd", "clip-rule": "evenodd", "d": "M14.431 3.323l-8.47 10-.79-.036-3.35-4.77.818-.574 2.978 4.24 8.051-9.506.764.646z" } }] }, { "tagName": "clipPath", "attrs": { "width": "16", "height": "16", "viewBox": "0 0 16 16", "fill": "currentColor", "id": "icon-close" }, "children": [{ "tagName": "path", "attrs": { "fill-rule": "evenodd", "clip-rule": "evenodd", "d": "M8 8.707l3.646 3.647.708-.707L8.707 8l3.647-3.646-.707-.708L8 7.293 4.354 3.646l-.707.708L7.293 8l-3.646 3.646.707.708L8 8.707z" } }] }, { "tagName": "clipPath", "attrs": { "width": "16", "height": "16", "viewBox": "0 0 16 16", "fill": "currentColor", "id": "icon-pass" }, "children": [{ "tagName": "path", "attrs": { "d": "M6.27 10.87h.71l4.56-4.56-.71-.71-4.2 4.21-1.92-1.92L4 8.6l2.27 2.27z" } }, { "tagName": "path", "attrs": { "fill-rule": "evenodd", "clip-rule": "evenodd", "d": "M8.6 1c1.6.1 3.1.9 4.2 2 1.3 1.4 2 3.1 2 5.1 0 1.6-.6 3.1-1.6 4.4-1 1.2-2.4 2.1-4 2.4-1.6.3-3.2.1-4.6-.7-1.4-.8-2.5-2-3.1-3.5C.9 9.2.8 7.5 1.3 6c.5-1.6 1.4-2.9 2.8-3.8C5.4 1.3 7 .9 8.6 1zm.5 12.9c1.3-.3 2.5-1 3.4-2.1.8-1.1 1.3-2.4 1.2-3.8 0-1.6-.6-3.2-1.7-4.3-1-1-2.2-1.6-3.6-1.7-1.3-.1-2.7.2-3.8 1-1.1.8-1.9 1.9-2.3 3.3-.4 1.3-.4 2.7.2 4 .6 1.3 1.5 2.3 2.7 3 1.2.7 2.6.9 3.9.6z" } }] }, { "tagName": "clipPath", "attrs": { "width": "16", "height": "16", "viewBox": "0 0 16 16", "fill": "currentColor", "id": "icon-gist" }, "children": [{ "tagName": "path", "attrs": { "fill-rule": "evenodd", "clip-rule": "evenodd", "d": "M10.57 1.14l3.28 3.3.15.36v9.7l-.5.5h-11l-.5-.5v-13l.5-.5h7.72l.35.14zM10 5h3l-3-3v3zM3 2v12h10V6H9.5L9 5.5V2H3zm2.062 7.533l1.817-1.828L6.17 7 4 9.179v.707l2.171 2.174.707-.707-1.816-1.82zM8.8 7.714l.7-.709 2.189 2.175v.709L9.5 12.062l-.705-.709 1.831-1.82L8.8 7.714z" } }] }] }] };\nvar clipPaths_default = svgJson;\n\n// packages/injected/src/recorder/recorder.ts\nvar HighlightColors = {\n  multiple: "#f6b26b7f",\n  single: "#6fa8dc7f",\n  assert: "#8acae480",\n  action: "#dc6f6f7f"\n};\nvar NoneTool = class {\n  cursor() {\n    return "default";\n  }\n};\nvar InspectTool = class {\n  constructor(recorder, assertVisibility) {\n    this._hoveredModel = null;\n    this._hoveredElement = null;\n    this._recorder = recorder;\n    this._assertVisibility = assertVisibility;\n  }\n  cursor() {\n    return "pointer";\n  }\n  uninstall() {\n    this._hoveredModel = null;\n    this._hoveredElement = null;\n  }\n  onClick(event) {\n    var _a;\n    consumeEvent(event);\n    if (event.button !== 0)\n      return;\n    if ((_a = this._hoveredModel) == null ? void 0 : _a.selector)\n      this._commit(this._hoveredModel.selector, this._hoveredModel);\n  }\n  onPointerDown(event) {\n    consumeEvent(event);\n  }\n  onPointerUp(event) {\n    consumeEvent(event);\n  }\n  onMouseDown(event) {\n    consumeEvent(event);\n  }\n  onMouseUp(event) {\n    consumeEvent(event);\n  }\n  onMouseMove(event) {\n    var _a;\n    consumeEvent(event);\n    let target = this._recorder.deepEventTarget(event);\n    if (!target.isConnected)\n      target = null;\n    if (this._hoveredElement === target)\n      return;\n    this._hoveredElement = target;\n    let model = null;\n    if (this._hoveredElement) {\n      const generated = this._recorder.injectedScript.generateSelector(this._hoveredElement, { testIdAttributeName: this._recorder.state.testIdAttributeName, multiple: false });\n      model = {\n        selector: generated.selector,\n        elements: generated.elements,\n        tooltipText: this._recorder.injectedScript.utils.asLocator(this._recorder.state.language, generated.selector),\n        color: this._assertVisibility ? HighlightColors.assert : HighlightColors.single\n      };\n    }\n    if (((_a = this._hoveredModel) == null ? void 0 : _a.selector) === (model == null ? void 0 : model.selector))\n      return;\n    this._hoveredModel = model;\n    this._recorder.updateHighlight(model, true);\n  }\n  onMouseEnter(event) {\n    consumeEvent(event);\n  }\n  onMouseLeave(event) {\n    consumeEvent(event);\n    const window = this._recorder.injectedScript.window;\n    if (window.top !== window && this._recorder.deepEventTarget(event).nodeType === Node.DOCUMENT_NODE)\n      this._reset(true);\n  }\n  onKeyDown(event) {\n    consumeEvent(event);\n    if (event.key === "Escape") {\n      if (this._assertVisibility)\n        this._recorder.setMode("recording");\n    }\n  }\n  onKeyUp(event) {\n    consumeEvent(event);\n  }\n  onScroll(event) {\n    this._reset(false);\n  }\n  _commit(selector, model) {\n    var _a;\n    if (this._assertVisibility) {\n      this._recorder.recordAction({\n        name: "assertVisible",\n        selector,\n        signals: []\n      });\n      this._recorder.setMode("recording");\n      (_a = this._recorder.overlay) == null ? void 0 : _a.flashToolSucceeded("assertingVisibility");\n    } else {\n      this._recorder.elementPicked(selector, model);\n    }\n  }\n  _reset(userGesture) {\n    this._hoveredElement = null;\n    this._hoveredModel = null;\n    this._recorder.updateHighlight(null, userGesture);\n  }\n};\nvar RecordActionTool = class {\n  constructor(recorder) {\n    this._hoveredModel = null;\n    this._hoveredElement = null;\n    this._activeModel = null;\n    this._expectProgrammaticKeyUp = false;\n    this._observer = null;\n    this._recorder = recorder;\n    this._performingActions = /* @__PURE__ */ new Set();\n  }\n  cursor() {\n    return "pointer";\n  }\n  _installObserverIfNeeded() {\n    var _a;\n    if (this._observer)\n      return;\n    if (!((_a = this._recorder.injectedScript.document) == null ? void 0 : _a.body))\n      return;\n    this._observer = new MutationObserver((mutations) => {\n      if (!this._hoveredElement)\n        return;\n      for (const mutation of mutations) {\n        for (const node of mutation.removedNodes) {\n          if (node === this._hoveredElement || node.contains(this._hoveredElement))\n            this._resetHoveredModel();\n        }\n      }\n    });\n    this._observer.observe(this._recorder.injectedScript.document.body, { childList: true, subtree: true });\n  }\n  uninstall() {\n    var _a;\n    (_a = this._observer) == null ? void 0 : _a.disconnect();\n    this._observer = null;\n    this._hoveredModel = null;\n    this._hoveredElement = null;\n    this._activeModel = null;\n    this._expectProgrammaticKeyUp = false;\n  }\n  onClick(event) {\n    if (isRangeInput(this._hoveredElement))\n      return;\n    if (event.button === 2 && event.type === "auxclick")\n      return;\n    if (this._shouldIgnoreMouseEvent(event))\n      return;\n    if (this._actionInProgress(event))\n      return;\n    if (this._consumedDueToNoModel(event, this._hoveredModel))\n      return;\n    const checkbox = asCheckbox(this._recorder.deepEventTarget(event));\n    if (checkbox && event.detail === 1) {\n      this._performAction({\n        name: checkbox.checked ? "check" : "uncheck",\n        selector: this._hoveredModel.selector,\n        signals: []\n      });\n      return;\n    }\n    this._cancelPendingClickAction();\n    if (event.detail === 1) {\n      this._pendingClickAction = {\n        action: {\n          name: "click",\n          selector: this._hoveredModel.selector,\n          position: positionForEvent(event),\n          signals: [],\n          button: buttonForEvent(event),\n          modifiers: modifiersForEvent(event),\n          clickCount: event.detail\n        },\n        timeout: this._recorder.injectedScript.utils.builtins.setTimeout(() => this._commitPendingClickAction(), 200)\n      };\n    }\n  }\n  onDblClick(event) {\n    if (isRangeInput(this._hoveredElement))\n      return;\n    if (this._shouldIgnoreMouseEvent(event))\n      return;\n    if (this._actionInProgress(event))\n      return;\n    if (this._consumedDueToNoModel(event, this._hoveredModel))\n      return;\n    this._cancelPendingClickAction();\n    this._performAction({\n      name: "click",\n      selector: this._hoveredModel.selector,\n      position: positionForEvent(event),\n      signals: [],\n      button: buttonForEvent(event),\n      modifiers: modifiersForEvent(event),\n      clickCount: event.detail\n    });\n  }\n  _commitPendingClickAction() {\n    if (this._pendingClickAction)\n      this._performAction(this._pendingClickAction.action);\n    this._cancelPendingClickAction();\n  }\n  _cancelPendingClickAction() {\n    if (this._pendingClickAction)\n      this._recorder.injectedScript.utils.builtins.clearTimeout(this._pendingClickAction.timeout);\n    this._pendingClickAction = void 0;\n  }\n  onContextMenu(event) {\n    if (this._shouldIgnoreMouseEvent(event))\n      return;\n    if (this._actionInProgress(event))\n      return;\n    if (this._consumedDueToNoModel(event, this._hoveredModel))\n      return;\n    this._performAction({\n      name: "click",\n      selector: this._hoveredModel.selector,\n      position: positionForEvent(event),\n      signals: [],\n      button: "right",\n      modifiers: 0,\n      clickCount: 0\n    });\n  }\n  onPointerDown(event) {\n    if (this._shouldIgnoreMouseEvent(event))\n      return;\n    this._consumeWhenAboutToPerform(event);\n  }\n  onPointerUp(event) {\n    if (this._shouldIgnoreMouseEvent(event))\n      return;\n    this._consumeWhenAboutToPerform(event);\n  }\n  onMouseDown(event) {\n    if (this._shouldIgnoreMouseEvent(event))\n      return;\n    this._consumeWhenAboutToPerform(event);\n    this._activeModel = this._hoveredModel;\n  }\n  onMouseUp(event) {\n    if (this._shouldIgnoreMouseEvent(event))\n      return;\n    this._consumeWhenAboutToPerform(event);\n  }\n  onMouseMove(event) {\n    const target = this._recorder.deepEventTarget(event);\n    if (this._hoveredElement === target)\n      return;\n    this._hoveredElement = target;\n    this._updateModelForHoveredElement();\n  }\n  onMouseLeave(event) {\n    const window = this._recorder.injectedScript.window;\n    if (window.top !== window && this._recorder.deepEventTarget(event).nodeType === Node.DOCUMENT_NODE) {\n      this._hoveredElement = null;\n      this._updateModelForHoveredElement();\n    }\n  }\n  onFocus(event) {\n    this._onFocus(true);\n  }\n  onInput(event) {\n    const target = this._recorder.deepEventTarget(event);\n    if (target.nodeName === "INPUT" && target.type.toLowerCase() === "file") {\n      this._recorder.recordAction({\n        name: "setInputFiles",\n        selector: this._activeModel.selector,\n        signals: [],\n        files: [...target.files || []].map((file) => file.name)\n      });\n      return;\n    }\n    if (isRangeInput(target)) {\n      this._recorder.recordAction({\n        name: "fill",\n        // must use hoveredModel instead of activeModel for it to work in webkit\n        selector: this._hoveredModel.selector,\n        signals: [],\n        text: target.value\n      });\n      return;\n    }\n    if (["INPUT", "TEXTAREA"].includes(target.nodeName) || target.isContentEditable) {\n      if (target.nodeName === "INPUT" && ["checkbox", "radio"].includes(target.type.toLowerCase())) {\n        return;\n      }\n      if (this._consumedDueWrongTarget(event))\n        return;\n      this._recorder.recordAction({\n        name: "fill",\n        selector: this._activeModel.selector,\n        signals: [],\n        text: target.isContentEditable ? target.innerText : target.value\n      });\n    }\n    if (target.nodeName === "SELECT") {\n      const selectElement = target;\n      this._recorder.recordAction({\n        name: "select",\n        selector: this._activeModel.selector,\n        options: [...selectElement.selectedOptions].map((option) => option.value),\n        signals: []\n      });\n    }\n  }\n  onKeyDown(event) {\n    if (!this._shouldGenerateKeyPressFor(event))\n      return;\n    if (this._actionInProgress(event)) {\n      this._expectProgrammaticKeyUp = true;\n      return;\n    }\n    if (this._consumedDueWrongTarget(event))\n      return;\n    if (event.key === " ") {\n      const checkbox = asCheckbox(this._recorder.deepEventTarget(event));\n      if (checkbox && event.detail === 0) {\n        this._performAction({\n          name: checkbox.checked ? "uncheck" : "check",\n          selector: this._activeModel.selector,\n          signals: []\n        });\n        return;\n      }\n    }\n    this._performAction({\n      name: "press",\n      selector: this._activeModel.selector,\n      signals: [],\n      key: event.key,\n      modifiers: modifiersForEvent(event)\n    });\n  }\n  onKeyUp(event) {\n    if (!this._shouldGenerateKeyPressFor(event))\n      return;\n    if (!this._expectProgrammaticKeyUp) {\n      consumeEvent(event);\n      return;\n    }\n    this._expectProgrammaticKeyUp = false;\n  }\n  onScroll(event) {\n    this._resetHoveredModel();\n  }\n  _resetHoveredModel() {\n    this._hoveredModel = null;\n    this._hoveredElement = null;\n    this._updateHighlight(false);\n  }\n  _onFocus(userGesture) {\n    const activeElement = deepActiveElement(this._recorder.document);\n    if (userGesture && activeElement === this._recorder.document.body)\n      return;\n    const result = activeElement ? this._recorder.injectedScript.generateSelector(activeElement, { testIdAttributeName: this._recorder.state.testIdAttributeName }) : null;\n    this._activeModel = result && result.selector ? { ...result, color: HighlightColors.action } : null;\n    if (userGesture) {\n      this._hoveredElement = activeElement;\n      this._updateModelForHoveredElement();\n    }\n  }\n  _shouldIgnoreMouseEvent(event) {\n    const target = this._recorder.deepEventTarget(event);\n    const nodeName = target.nodeName;\n    if (nodeName === "SELECT" || nodeName === "OPTION")\n      return true;\n    if (nodeName === "INPUT" && ["date", "range"].includes(target.type))\n      return true;\n    return false;\n  }\n  _actionInProgress(event) {\n    const isKeyEvent = event instanceof KeyboardEvent;\n    const isMouseOrPointerEvent = event instanceof MouseEvent || event instanceof PointerEvent;\n    for (const action of this._performingActions) {\n      if (isKeyEvent && action.name === "press" && event.key === action.key)\n        return true;\n      if (isMouseOrPointerEvent && (action.name === "click" || action.name === "check" || action.name === "uncheck"))\n        return true;\n    }\n    consumeEvent(event);\n    return false;\n  }\n  _consumedDueToNoModel(event, model) {\n    if (model)\n      return false;\n    consumeEvent(event);\n    return true;\n  }\n  _consumedDueWrongTarget(event) {\n    if (this._activeModel && this._activeModel.elements[0] === this._recorder.deepEventTarget(event))\n      return false;\n    consumeEvent(event);\n    return true;\n  }\n  _consumeWhenAboutToPerform(event) {\n    if (!this._performingActions.size)\n      consumeEvent(event);\n  }\n  _performAction(action) {\n    this._recorder.updateHighlight(null, false);\n    this._performingActions.add(action);\n    const promise = this._recorder.performAction(action).then(() => {\n      this._performingActions.delete(action);\n      this._onFocus(false);\n    });\n    if (!this._recorder.injectedScript.isUnderTest)\n      return;\n    void promise.then(() => {\n      console.error("Action performed for test: " + JSON.stringify({\n        // eslint-disable-line no-console\n        hovered: this._hoveredModel ? this._hoveredModel.selector : null,\n        active: this._activeModel ? this._activeModel.selector : null\n      }));\n    });\n  }\n  _shouldGenerateKeyPressFor(event) {\n    if (typeof event.key !== "string")\n      return false;\n    if (event.key === "Enter" && (this._recorder.deepEventTarget(event).nodeName === "TEXTAREA" || this._recorder.deepEventTarget(event).isContentEditable))\n      return false;\n    if (["Backspace", "Delete", "AltGraph"].includes(event.key))\n      return false;\n    if (event.key === "@" && event.code === "KeyL")\n      return false;\n    if (navigator.platform.includes("Mac")) {\n      if (event.key === "v" && event.metaKey)\n        return false;\n    } else {\n      if (event.key === "v" && event.ctrlKey)\n        return false;\n      if (event.key === "Insert" && event.shiftKey)\n        return false;\n    }\n    if (["Shift", "Control", "Meta", "Alt", "Process"].includes(event.key))\n      return false;\n    const hasModifier = event.ctrlKey || event.altKey || event.metaKey;\n    if (event.key.length === 1 && !hasModifier)\n      return !!asCheckbox(this._recorder.deepEventTarget(event));\n    return true;\n  }\n  _updateModelForHoveredElement() {\n    this._installObserverIfNeeded();\n    if (this._performingActions.size)\n      return;\n    if (!this._hoveredElement || !this._hoveredElement.isConnected) {\n      this._hoveredModel = null;\n      this._hoveredElement = null;\n      this._updateHighlight(true);\n      return;\n    }\n    const { selector, elements } = this._recorder.injectedScript.generateSelector(this._hoveredElement, { testIdAttributeName: this._recorder.state.testIdAttributeName });\n    if (this._hoveredModel && this._hoveredModel.selector === selector)\n      return;\n    this._hoveredModel = selector ? { selector, elements, color: HighlightColors.action } : null;\n    this._updateHighlight(true);\n  }\n  _updateHighlight(userGesture) {\n    this._recorder.updateHighlight(this._hoveredModel, userGesture);\n  }\n};\nvar JsonRecordActionTool = class {\n  constructor(recorder) {\n    this._recorder = recorder;\n  }\n  cursor() {\n    return "pointer";\n  }\n  onClick(event) {\n    const element = this._recorder.deepEventTarget(event);\n    if (isRangeInput(element))\n      return;\n    if (event.button === 2 && event.type === "auxclick")\n      return;\n    if (this._shouldIgnoreMouseEvent(event))\n      return;\n    const checkbox = asCheckbox(element);\n    const { ariaSnapshot, selector } = this._ariaSnapshot(element);\n    if (checkbox && event.detail === 1) {\n      this._recorder.recordAction({\n        name: checkbox.checked ? "check" : "uncheck",\n        selector,\n        signals: [],\n        ariaSnapshot\n      });\n      return;\n    }\n    this._recorder.recordAction({\n      name: "click",\n      selector,\n      ariaSnapshot,\n      position: positionForEvent(event),\n      signals: [],\n      button: buttonForEvent(event),\n      modifiers: modifiersForEvent(event),\n      clickCount: event.detail\n    });\n  }\n  onDblClick(event) {\n    const element = this._recorder.deepEventTarget(event);\n    if (isRangeInput(element))\n      return;\n    if (this._shouldIgnoreMouseEvent(event))\n      return;\n    const { ariaSnapshot, selector } = this._ariaSnapshot(element);\n    this._recorder.recordAction({\n      name: "click",\n      selector,\n      ariaSnapshot,\n      position: positionForEvent(event),\n      signals: [],\n      button: buttonForEvent(event),\n      modifiers: modifiersForEvent(event),\n      clickCount: event.detail\n    });\n  }\n  onInput(event) {\n    const element = this._recorder.deepEventTarget(event);\n    const { ariaSnapshot, selector } = this._ariaSnapshot(element);\n    if (isRangeInput(element)) {\n      this._recorder.recordAction({\n        name: "fill",\n        selector,\n        ariaSnapshot,\n        signals: [],\n        text: element.value\n      });\n      return;\n    }\n    if (["INPUT", "TEXTAREA"].includes(element.nodeName) || element.isContentEditable) {\n      if (element.nodeName === "INPUT" && ["checkbox", "radio"].includes(element.type.toLowerCase())) {\n        return;\n      }\n      this._recorder.recordAction({\n        name: "fill",\n        selector,\n        ariaSnapshot,\n        signals: [],\n        text: element.isContentEditable ? element.innerText : element.value\n      });\n      return;\n    }\n    if (element.nodeName === "SELECT") {\n      const selectElement = element;\n      this._recorder.recordAction({\n        name: "select",\n        selector,\n        ariaSnapshot,\n        options: [...selectElement.selectedOptions].map((option) => option.value),\n        signals: []\n      });\n      return;\n    }\n  }\n  onKeyDown(event) {\n    if (!this._shouldGenerateKeyPressFor(event))\n      return;\n    const element = this._recorder.deepEventTarget(event);\n    const { ariaSnapshot, selector } = this._ariaSnapshot(element);\n    if (event.key === " ") {\n      const checkbox = asCheckbox(element);\n      if (checkbox && event.detail === 0) {\n        this._recorder.recordAction({\n          name: checkbox.checked ? "uncheck" : "check",\n          selector,\n          ariaSnapshot,\n          signals: []\n        });\n        return;\n      }\n    }\n    this._recorder.recordAction({\n      name: "press",\n      selector,\n      ariaSnapshot,\n      signals: [],\n      key: event.key,\n      modifiers: modifiersForEvent(event)\n    });\n  }\n  _shouldIgnoreMouseEvent(event) {\n    const target = this._recorder.deepEventTarget(event);\n    const nodeName = target.nodeName;\n    if (nodeName === "SELECT" || nodeName === "OPTION")\n      return true;\n    if (nodeName === "INPUT" && ["date", "range"].includes(target.type))\n      return true;\n    return false;\n  }\n  _shouldGenerateKeyPressFor(event) {\n    if (typeof event.key !== "string")\n      return false;\n    if (event.key === "Enter" && (this._recorder.deepEventTarget(event).nodeName === "TEXTAREA" || this._recorder.deepEventTarget(event).isContentEditable))\n      return false;\n    if (["Backspace", "Delete", "AltGraph"].includes(event.key))\n      return false;\n    if (event.key === "@" && event.code === "KeyL")\n      return false;\n    if (navigator.platform.includes("Mac")) {\n      if (event.key === "v" && event.metaKey)\n        return false;\n    } else {\n      if (event.key === "v" && event.ctrlKey)\n        return false;\n      if (event.key === "Insert" && event.shiftKey)\n        return false;\n    }\n    if (["Shift", "Control", "Meta", "Alt", "Process"].includes(event.key))\n      return false;\n    const hasModifier = event.ctrlKey || event.altKey || event.metaKey;\n    if (event.key.length === 1 && !hasModifier)\n      return !this._isEditable(this._recorder.deepEventTarget(event));\n    return true;\n  }\n  _isEditable(element) {\n    if (element.nodeName === "TEXTAREA" || element.nodeName === "INPUT")\n      return true;\n    if (element.isContentEditable)\n      return true;\n    return false;\n  }\n  _ariaSnapshot(element) {\n    const { ariaSnapshot, refs } = this._recorder.injectedScript.ariaSnapshotForRecorder();\n    const ref = element ? refs.get(element) : void 0;\n    const selector = ref ? `aria-ref=${ref}` : void 0;\n    return { ariaSnapshot, selector };\n  }\n};\nvar TextAssertionTool = class {\n  constructor(recorder, kind) {\n    this._hoverHighlight = null;\n    this._action = null;\n    this._recorder = recorder;\n    this._textCache = /* @__PURE__ */ new Map();\n    this._kind = kind;\n    this._dialog = new Dialog(recorder);\n  }\n  cursor() {\n    return "pointer";\n  }\n  uninstall() {\n    this._dialog.close();\n    this._hoverHighlight = null;\n  }\n  onClick(event) {\n    consumeEvent(event);\n    if (this._kind === "value") {\n      this._commitAssertValue();\n    } else {\n      if (!this._dialog.isShowing())\n        this._showDialog();\n    }\n  }\n  onMouseDown(event) {\n    const target = this._recorder.deepEventTarget(event);\n    if (this._elementHasValue(target))\n      event.preventDefault();\n  }\n  onPointerUp(event) {\n    var _a;\n    const target = (_a = this._hoverHighlight) == null ? void 0 : _a.elements[0];\n    if (this._kind === "value" && target && (target.nodeName === "INPUT" || target.nodeName === "SELECT") && target.disabled) {\n      this._commitAssertValue();\n    }\n  }\n  onMouseMove(event) {\n    var _a;\n    if (this._dialog.isShowing())\n      return;\n    const target = this._recorder.deepEventTarget(event);\n    if (((_a = this._hoverHighlight) == null ? void 0 : _a.elements[0]) === target)\n      return;\n    if (this._kind === "text" || this._kind === "snapshot") {\n      this._hoverHighlight = this._recorder.injectedScript.utils.elementText(this._textCache, target).full ? { elements: [target], selector: "", color: HighlightColors.assert } : null;\n    } else if (this._elementHasValue(target)) {\n      const generated = this._recorder.injectedScript.generateSelector(target, { testIdAttributeName: this._recorder.state.testIdAttributeName });\n      this._hoverHighlight = { selector: generated.selector, elements: generated.elements, color: HighlightColors.assert };\n    } else {\n      this._hoverHighlight = null;\n    }\n    this._recorder.updateHighlight(this._hoverHighlight, true);\n  }\n  onKeyDown(event) {\n    if (event.key === "Escape")\n      this._recorder.setMode("recording");\n    consumeEvent(event);\n  }\n  onScroll(event) {\n    this._recorder.updateHighlight(this._hoverHighlight, false);\n  }\n  _elementHasValue(element) {\n    return element.nodeName === "TEXTAREA" || element.nodeName === "SELECT" || element.nodeName === "INPUT" && !["button", "image", "reset", "submit"].includes(element.type);\n  }\n  _generateAction() {\n    var _a;\n    this._textCache.clear();\n    const target = (_a = this._hoverHighlight) == null ? void 0 : _a.elements[0];\n    if (!target)\n      return null;\n    if (this._kind === "value") {\n      if (!this._elementHasValue(target))\n        return null;\n      const { selector } = this._recorder.injectedScript.generateSelector(target, { testIdAttributeName: this._recorder.state.testIdAttributeName });\n      if (target.nodeName === "INPUT" && ["checkbox", "radio"].includes(target.type.toLowerCase())) {\n        return {\n          name: "assertChecked",\n          selector,\n          signals: [],\n          // Interestingly, inputElement.checked is reversed inside this event handler.\n          checked: !target.checked\n        };\n      } else {\n        return {\n          name: "assertValue",\n          selector,\n          signals: [],\n          value: target.value\n        };\n      }\n    } else if (this._kind === "snapshot") {\n      const generated = this._recorder.injectedScript.generateSelector(target, { testIdAttributeName: this._recorder.state.testIdAttributeName, forTextExpect: true });\n      this._hoverHighlight = { selector: generated.selector, elements: generated.elements, color: HighlightColors.assert };\n      this._recorder.updateHighlight(this._hoverHighlight, true);\n      return {\n        name: "assertSnapshot",\n        selector: this._hoverHighlight.selector,\n        signals: [],\n        ariaSnapshot: this._recorder.injectedScript.ariaSnapshot(target, { mode: "regex" })\n      };\n    } else {\n      const generated = this._recorder.injectedScript.generateSelector(target, { testIdAttributeName: this._recorder.state.testIdAttributeName, forTextExpect: true });\n      this._hoverHighlight = { selector: generated.selector, elements: generated.elements, color: HighlightColors.assert };\n      this._recorder.updateHighlight(this._hoverHighlight, true);\n      return {\n        name: "assertText",\n        selector: this._hoverHighlight.selector,\n        signals: [],\n        text: this._recorder.injectedScript.utils.elementText(this._textCache, target).normalized,\n        substring: true\n      };\n    }\n  }\n  _renderValue(action) {\n    if ((action == null ? void 0 : action.name) === "assertText")\n      return this._recorder.injectedScript.utils.normalizeWhiteSpace(action.text);\n    if ((action == null ? void 0 : action.name) === "assertChecked")\n      return String(action.checked);\n    if ((action == null ? void 0 : action.name) === "assertValue")\n      return action.value;\n    if ((action == null ? void 0 : action.name) === "assertSnapshot")\n      return action.ariaSnapshot;\n    return "";\n  }\n  _commit() {\n    if (!this._action || !this._dialog.isShowing())\n      return;\n    this._dialog.close();\n    this._recorder.recordAction(this._action);\n    this._recorder.setMode("recording");\n  }\n  _showDialog() {\n    var _a, _b, _c, _d;\n    if (!((_a = this._hoverHighlight) == null ? void 0 : _a.elements[0]))\n      return;\n    this._action = this._generateAction();\n    if (((_b = this._action) == null ? void 0 : _b.name) === "assertText") {\n      this._showTextDialog(this._action);\n    } else if (((_c = this._action) == null ? void 0 : _c.name) === "assertSnapshot") {\n      this._recorder.recordAction(this._action);\n      this._recorder.setMode("recording");\n      (_d = this._recorder.overlay) == null ? void 0 : _d.flashToolSucceeded("assertingSnapshot");\n    }\n  }\n  _showTextDialog(action) {\n    const textElement = this._recorder.document.createElement("textarea");\n    textElement.setAttribute("spellcheck", "false");\n    textElement.value = this._renderValue(action);\n    textElement.classList.add("text-editor");\n    const updateAndValidate = () => {\n      var _a;\n      const newValue = this._recorder.injectedScript.utils.normalizeWhiteSpace(textElement.value);\n      const target = (_a = this._hoverHighlight) == null ? void 0 : _a.elements[0];\n      if (!target)\n        return;\n      action.text = newValue;\n      const targetText = this._recorder.injectedScript.utils.elementText(this._textCache, target).normalized;\n      const matches = newValue && targetText.includes(newValue);\n      textElement.classList.toggle("does-not-match", !matches);\n    };\n    textElement.addEventListener("input", updateAndValidate);\n    const label = "Assert that element contains text";\n    const dialogElement = this._dialog.show({\n      label,\n      body: textElement,\n      onCommit: () => this._commit()\n    });\n    const position = this._recorder.highlight.tooltipPosition(this._recorder.highlight.firstBox(), dialogElement);\n    this._dialog.moveTo(position.anchorTop, position.anchorLeft);\n    textElement.focus();\n  }\n  _commitAssertValue() {\n    var _a;\n    if (this._kind !== "value")\n      return;\n    const action = this._generateAction();\n    if (!action)\n      return;\n    this._recorder.recordAction(action);\n    this._recorder.setMode("recording");\n    (_a = this._recorder.overlay) == null ? void 0 : _a.flashToolSucceeded("assertingValue");\n  }\n};\nvar Overlay = class {\n  constructor(recorder) {\n    this._listeners = [];\n    this._offsetX = 0;\n    this._measure = { width: 0, height: 0 };\n    this._recorder = recorder;\n    const document = this._recorder.document;\n    this._overlayElement = document.createElement("x-pw-overlay");\n    const toolsListElement = document.createElement("x-pw-tools-list");\n    this._overlayElement.appendChild(toolsListElement);\n    this._dragHandle = document.createElement("x-pw-tool-gripper");\n    this._dragHandle.appendChild(document.createElement("x-div"));\n    toolsListElement.appendChild(this._dragHandle);\n    this._recordToggle = this._recorder.document.createElement("x-pw-tool-item");\n    this._recordToggle.title = "Record";\n    this._recordToggle.classList.add("record");\n    this._recordToggle.appendChild(this._recorder.document.createElement("x-div"));\n    toolsListElement.appendChild(this._recordToggle);\n    this._pickLocatorToggle = this._recorder.document.createElement("x-pw-tool-item");\n    this._pickLocatorToggle.title = "Pick locator";\n    this._pickLocatorToggle.classList.add("pick-locator");\n    this._pickLocatorToggle.appendChild(this._recorder.document.createElement("x-div"));\n    toolsListElement.appendChild(this._pickLocatorToggle);\n    this._assertVisibilityToggle = this._recorder.document.createElement("x-pw-tool-item");\n    this._assertVisibilityToggle.title = "Assert visibility";\n    this._assertVisibilityToggle.classList.add("visibility");\n    this._assertVisibilityToggle.appendChild(this._recorder.document.createElement("x-div"));\n    toolsListElement.appendChild(this._assertVisibilityToggle);\n    this._assertTextToggle = this._recorder.document.createElement("x-pw-tool-item");\n    this._assertTextToggle.title = "Assert text";\n    this._assertTextToggle.classList.add("text");\n    this._assertTextToggle.appendChild(this._recorder.document.createElement("x-div"));\n    toolsListElement.appendChild(this._assertTextToggle);\n    this._assertValuesToggle = this._recorder.document.createElement("x-pw-tool-item");\n    this._assertValuesToggle.title = "Assert value";\n    this._assertValuesToggle.classList.add("value");\n    this._assertValuesToggle.appendChild(this._recorder.document.createElement("x-div"));\n    toolsListElement.appendChild(this._assertValuesToggle);\n    this._assertSnapshotToggle = this._recorder.document.createElement("x-pw-tool-item");\n    this._assertSnapshotToggle.title = "Assert snapshot";\n    this._assertSnapshotToggle.classList.add("snapshot");\n    this._assertSnapshotToggle.appendChild(this._recorder.document.createElement("x-div"));\n    toolsListElement.appendChild(this._assertSnapshotToggle);\n    this._updateVisualPosition();\n    this._refreshListeners();\n  }\n  _refreshListeners() {\n    removeEventListeners(this._listeners);\n    this._listeners = [\n      addEventListener(this._dragHandle, "mousedown", (event) => {\n        this._dragState = { offsetX: this._offsetX, dragStart: { x: event.clientX, y: 0 } };\n      }),\n      addEventListener(this._recordToggle, "click", () => {\n        if (this._recordToggle.classList.contains("disabled"))\n          return;\n        this._recorder.setMode(this._recorder.state.mode === "none" || this._recorder.state.mode === "standby" || this._recorder.state.mode === "inspecting" ? "recording" : "standby");\n      }),\n      addEventListener(this._pickLocatorToggle, "click", () => {\n        if (this._pickLocatorToggle.classList.contains("disabled"))\n          return;\n        const newMode = {\n          "inspecting": "standby",\n          "none": "inspecting",\n          "standby": "inspecting",\n          "recording": "recording-inspecting",\n          "recording-inspecting": "recording",\n          "assertingText": "recording-inspecting",\n          "assertingVisibility": "recording-inspecting",\n          "assertingValue": "recording-inspecting",\n          "assertingSnapshot": "recording-inspecting"\n        };\n        this._recorder.setMode(newMode[this._recorder.state.mode]);\n      }),\n      addEventListener(this._assertVisibilityToggle, "click", () => {\n        if (!this._assertVisibilityToggle.classList.contains("disabled"))\n          this._recorder.setMode(this._recorder.state.mode === "assertingVisibility" ? "recording" : "assertingVisibility");\n      }),\n      addEventListener(this._assertTextToggle, "click", () => {\n        if (!this._assertTextToggle.classList.contains("disabled"))\n          this._recorder.setMode(this._recorder.state.mode === "assertingText" ? "recording" : "assertingText");\n      }),\n      addEventListener(this._assertValuesToggle, "click", () => {\n        if (!this._assertValuesToggle.classList.contains("disabled"))\n          this._recorder.setMode(this._recorder.state.mode === "assertingValue" ? "recording" : "assertingValue");\n      }),\n      addEventListener(this._assertSnapshotToggle, "click", () => {\n        if (!this._assertSnapshotToggle.classList.contains("disabled"))\n          this._recorder.setMode(this._recorder.state.mode === "assertingSnapshot" ? "recording" : "assertingSnapshot");\n      })\n    ];\n  }\n  install() {\n    this._recorder.highlight.appendChild(this._overlayElement);\n    this._refreshListeners();\n    this._updateVisualPosition();\n  }\n  contains(element) {\n    return this._recorder.injectedScript.utils.isInsideScope(this._overlayElement, element);\n  }\n  setUIState(state) {\n    this._recordToggle.classList.toggle("toggled", state.mode === "recording" || state.mode === "assertingText" || state.mode === "assertingVisibility" || state.mode === "assertingValue" || state.mode === "assertingSnapshot" || state.mode === "recording-inspecting");\n    this._pickLocatorToggle.classList.toggle("toggled", state.mode === "inspecting" || state.mode === "recording-inspecting");\n    this._assertVisibilityToggle.classList.toggle("toggled", state.mode === "assertingVisibility");\n    this._assertVisibilityToggle.classList.toggle("disabled", state.mode === "none" || state.mode === "standby" || state.mode === "inspecting");\n    this._assertTextToggle.classList.toggle("toggled", state.mode === "assertingText");\n    this._assertTextToggle.classList.toggle("disabled", state.mode === "none" || state.mode === "standby" || state.mode === "inspecting");\n    this._assertValuesToggle.classList.toggle("toggled", state.mode === "assertingValue");\n    this._assertValuesToggle.classList.toggle("disabled", state.mode === "none" || state.mode === "standby" || state.mode === "inspecting");\n    this._assertSnapshotToggle.classList.toggle("toggled", state.mode === "assertingSnapshot");\n    this._assertSnapshotToggle.classList.toggle("disabled", state.mode === "none" || state.mode === "standby" || state.mode === "inspecting");\n    if (this._offsetX !== state.overlay.offsetX) {\n      this._offsetX = state.overlay.offsetX;\n      this._updateVisualPosition();\n    }\n    if (state.mode === "none")\n      this._hideOverlay();\n    else\n      this._showOverlay();\n  }\n  flashToolSucceeded(tool) {\n    let element;\n    if (tool === "assertingVisibility")\n      element = this._assertVisibilityToggle;\n    else if (tool === "assertingSnapshot")\n      element = this._assertSnapshotToggle;\n    else\n      element = this._assertValuesToggle;\n    element.classList.add("succeeded");\n    this._recorder.injectedScript.utils.builtins.setTimeout(() => element.classList.remove("succeeded"), 2e3);\n  }\n  _hideOverlay() {\n    this._overlayElement.setAttribute("hidden", "true");\n  }\n  _showOverlay() {\n    if (!this._overlayElement.hasAttribute("hidden"))\n      return;\n    this._overlayElement.removeAttribute("hidden");\n    this._updateVisualPosition();\n  }\n  _updateVisualPosition() {\n    this._measure = this._overlayElement.getBoundingClientRect();\n    this._overlayElement.style.left = (this._recorder.injectedScript.window.innerWidth - this._measure.width) / 2 + this._offsetX + "px";\n  }\n  onMouseMove(event) {\n    if (!event.buttons) {\n      this._dragState = void 0;\n      return false;\n    }\n    if (this._dragState) {\n      this._offsetX = this._dragState.offsetX + event.clientX - this._dragState.dragStart.x;\n      const halfGapSize = (this._recorder.injectedScript.window.innerWidth - this._measure.width) / 2 - 10;\n      this._offsetX = Math.max(-halfGapSize, Math.min(halfGapSize, this._offsetX));\n      this._updateVisualPosition();\n      this._recorder.setOverlayState({ offsetX: this._offsetX });\n      consumeEvent(event);\n      return true;\n    }\n    return false;\n  }\n  onMouseUp(event) {\n    if (this._dragState) {\n      consumeEvent(event);\n      return true;\n    }\n    return false;\n  }\n  onClick(event) {\n    if (this._dragState) {\n      this._dragState = void 0;\n      consumeEvent(event);\n      return true;\n    }\n    return false;\n  }\n  onDblClick(event) {\n    return false;\n  }\n};\nvar Recorder = class {\n  constructor(injectedScript, options) {\n    this._listeners = [];\n    this._lastHighlightedSelector = void 0;\n    this._lastHighlightedAriaTemplateJSON = "undefined";\n    this.state = {\n      mode: "none",\n      testIdAttributeName: "data-testid",\n      language: "javascript",\n      overlay: { offsetX: 0 }\n    };\n    this._delegate = {};\n    var _a, _b;\n    this.document = injectedScript.document;\n    this.injectedScript = injectedScript;\n    this.highlight = injectedScript.createHighlight();\n    this._tools = {\n      "none": new NoneTool(),\n      "standby": new NoneTool(),\n      "inspecting": new InspectTool(this, false),\n      "recording": (options == null ? void 0 : options.recorderMode) === "api" ? new JsonRecordActionTool(this) : new RecordActionTool(this),\n      "recording-inspecting": new InspectTool(this, false),\n      "assertingText": new TextAssertionTool(this, "text"),\n      "assertingVisibility": new InspectTool(this, true),\n      "assertingValue": new TextAssertionTool(this, "value"),\n      "assertingSnapshot": new TextAssertionTool(this, "snapshot")\n    };\n    this._currentTool = this._tools.none;\n    (_b = (_a = this._currentTool).install) == null ? void 0 : _b.call(_a);\n    if (injectedScript.window.top === injectedScript.window) {\n      this.overlay = new Overlay(this);\n      this.overlay.setUIState(this.state);\n    }\n    this._stylesheet = new injectedScript.window.CSSStyleSheet();\n    this._stylesheet.replaceSync(`\n      body[data-pw-cursor=pointer] *, body[data-pw-cursor=pointer] *::after { cursor: pointer !important; }\n      body[data-pw-cursor=text] *, body[data-pw-cursor=text] *::after { cursor: text !important; }\n    `);\n    this.installListeners();\n    injectedScript.utils.cacheNormalizedWhitespaces();\n    if (injectedScript.isUnderTest)\n      console.error("Recorder script ready for test");\n    injectedScript.consoleApi.install();\n  }\n  installListeners() {\n    var _a, _b, _c;\n    removeEventListeners(this._listeners);\n    this._listeners = [\n      addEventListener(this.document, "click", (event) => this._onClick(event), true),\n      addEventListener(this.document, "auxclick", (event) => this._onClick(event), true),\n      addEventListener(this.document, "dblclick", (event) => this._onDblClick(event), true),\n      addEventListener(this.document, "contextmenu", (event) => this._onContextMenu(event), true),\n      addEventListener(this.document, "dragstart", (event) => this._onDragStart(event), true),\n      addEventListener(this.document, "input", (event) => this._onInput(event), true),\n      addEventListener(this.document, "keydown", (event) => this._onKeyDown(event), true),\n      addEventListener(this.document, "keyup", (event) => this._onKeyUp(event), true),\n      addEventListener(this.document, "pointerdown", (event) => this._onPointerDown(event), true),\n      addEventListener(this.document, "pointerup", (event) => this._onPointerUp(event), true),\n      addEventListener(this.document, "mousedown", (event) => this._onMouseDown(event), true),\n      addEventListener(this.document, "mouseup", (event) => this._onMouseUp(event), true),\n      addEventListener(this.document, "mousemove", (event) => this._onMouseMove(event), true),\n      addEventListener(this.document, "mouseleave", (event) => this._onMouseLeave(event), true),\n      addEventListener(this.document, "mouseenter", (event) => this._onMouseEnter(event), true),\n      addEventListener(this.document, "focus", (event) => this._onFocus(event), true),\n      addEventListener(this.document, "scroll", (event) => this._onScroll(event), true)\n    ];\n    this.highlight.install();\n    let recreationInterval;\n    const recreate = () => {\n      this.highlight.install();\n      recreationInterval = this.injectedScript.utils.builtins.setTimeout(recreate, 500);\n    };\n    recreationInterval = this.injectedScript.utils.builtins.setTimeout(recreate, 500);\n    this._listeners.push(() => this.injectedScript.utils.builtins.clearTimeout(recreationInterval));\n    this.highlight.appendChild(createSvgElement(this.document, clipPaths_default));\n    (_a = this.overlay) == null ? void 0 : _a.install();\n    (_c = (_b = this._currentTool) == null ? void 0 : _b.install) == null ? void 0 : _c.call(_b);\n    this.document.adoptedStyleSheets.push(this._stylesheet);\n  }\n  _switchCurrentTool() {\n    var _a, _b, _c, _d, _e;\n    const newTool = this._tools[this.state.mode];\n    if (newTool === this._currentTool)\n      return;\n    (_b = (_a = this._currentTool).uninstall) == null ? void 0 : _b.call(_a);\n    this.clearHighlight();\n    this._currentTool = newTool;\n    (_d = (_c = this._currentTool).install) == null ? void 0 : _d.call(_c);\n    (_e = this.injectedScript.document.body) == null ? void 0 : _e.setAttribute("data-pw-cursor", newTool.cursor());\n  }\n  setUIState(state, delegate) {\n    var _a;\n    this._delegate = delegate;\n    if (state.actionPoint && this.state.actionPoint && state.actionPoint.x === this.state.actionPoint.x && state.actionPoint.y === this.state.actionPoint.y) {\n    } else if (!state.actionPoint && !this.state.actionPoint) {\n    } else {\n      if (state.actionPoint)\n        this.highlight.showActionPoint(state.actionPoint.x, state.actionPoint.y);\n      else\n        this.highlight.hideActionPoint();\n    }\n    this.state = state;\n    this.highlight.setLanguage(state.language);\n    this._switchCurrentTool();\n    (_a = this.overlay) == null ? void 0 : _a.setUIState(state);\n    let highlight = "noop";\n    if (state.actionSelector !== this._lastHighlightedSelector) {\n      const entries = state.actionSelector ? entriesForSelectorHighlight(this.injectedScript, state.language, state.actionSelector, this.document) : null;\n      highlight = (entries == null ? void 0 : entries.length) ? entries : "clear";\n      this._lastHighlightedSelector = (entries == null ? void 0 : entries.length) ? state.actionSelector : void 0;\n    }\n    const ariaTemplateJSON = JSON.stringify(state.ariaTemplate);\n    if (this._lastHighlightedAriaTemplateJSON !== ariaTemplateJSON) {\n      const elements = state.ariaTemplate ? this.injectedScript.getAllByAria(this.document, state.ariaTemplate) : [];\n      if (elements.length) {\n        const color = elements.length > 1 ? HighlightColors.multiple : HighlightColors.single;\n        highlight = elements.map((element) => ({ element, color }));\n        this._lastHighlightedAriaTemplateJSON = ariaTemplateJSON;\n      } else {\n        if (!this._lastHighlightedSelector)\n          highlight = "clear";\n        this._lastHighlightedAriaTemplateJSON = "undefined";\n      }\n    }\n    if (highlight === "clear")\n      this.highlight.clearHighlight();\n    else if (highlight !== "noop")\n      this.highlight.updateHighlight(highlight);\n  }\n  clearHighlight() {\n    this.updateHighlight(null, false);\n  }\n  _onClick(event) {\n    var _a, _b, _c;\n    if (!event.isTrusted)\n      return;\n    if ((_a = this.overlay) == null ? void 0 : _a.onClick(event))\n      return;\n    if (this._ignoreOverlayEvent(event))\n      return;\n    (_c = (_b = this._currentTool).onClick) == null ? void 0 : _c.call(_b, event);\n  }\n  _onDblClick(event) {\n    var _a, _b, _c;\n    if (!event.isTrusted)\n      return;\n    if ((_a = this.overlay) == null ? void 0 : _a.onDblClick(event))\n      return;\n    if (this._ignoreOverlayEvent(event))\n      return;\n    (_c = (_b = this._currentTool).onDblClick) == null ? void 0 : _c.call(_b, event);\n  }\n  _onContextMenu(event) {\n    var _a, _b;\n    if (!event.isTrusted)\n      return;\n    if (this._ignoreOverlayEvent(event))\n      return;\n    (_b = (_a = this._currentTool).onContextMenu) == null ? void 0 : _b.call(_a, event);\n  }\n  _onDragStart(event) {\n    var _a, _b;\n    if (!event.isTrusted)\n      return;\n    if (this._ignoreOverlayEvent(event))\n      return;\n    (_b = (_a = this._currentTool).onDragStart) == null ? void 0 : _b.call(_a, event);\n  }\n  _onPointerDown(event) {\n    var _a, _b;\n    if (!event.isTrusted)\n      return;\n    if (this._ignoreOverlayEvent(event))\n      return;\n    (_b = (_a = this._currentTool).onPointerDown) == null ? void 0 : _b.call(_a, event);\n  }\n  _onPointerUp(event) {\n    var _a, _b;\n    if (!event.isTrusted)\n      return;\n    if (this._ignoreOverlayEvent(event))\n      return;\n    (_b = (_a = this._currentTool).onPointerUp) == null ? void 0 : _b.call(_a, event);\n  }\n  _onMouseDown(event) {\n    var _a, _b;\n    if (!event.isTrusted)\n      return;\n    if (this._ignoreOverlayEvent(event))\n      return;\n    (_b = (_a = this._currentTool).onMouseDown) == null ? void 0 : _b.call(_a, event);\n  }\n  _onMouseUp(event) {\n    var _a, _b, _c;\n    if (!event.isTrusted)\n      return;\n    if ((_a = this.overlay) == null ? void 0 : _a.onMouseUp(event))\n      return;\n    if (this._ignoreOverlayEvent(event))\n      return;\n    (_c = (_b = this._currentTool).onMouseUp) == null ? void 0 : _c.call(_b, event);\n  }\n  _onMouseMove(event) {\n    var _a, _b, _c;\n    if (!event.isTrusted)\n      return;\n    if ((_a = this.overlay) == null ? void 0 : _a.onMouseMove(event))\n      return;\n    if (this._ignoreOverlayEvent(event))\n      return;\n    (_c = (_b = this._currentTool).onMouseMove) == null ? void 0 : _c.call(_b, event);\n  }\n  _onMouseEnter(event) {\n    var _a, _b;\n    if (!event.isTrusted)\n      return;\n    if (this._ignoreOverlayEvent(event))\n      return;\n    (_b = (_a = this._currentTool).onMouseEnter) == null ? void 0 : _b.call(_a, event);\n  }\n  _onMouseLeave(event) {\n    var _a, _b;\n    if (!event.isTrusted)\n      return;\n    if (this._ignoreOverlayEvent(event))\n      return;\n    (_b = (_a = this._currentTool).onMouseLeave) == null ? void 0 : _b.call(_a, event);\n  }\n  _onFocus(event) {\n    var _a, _b;\n    if (!event.isTrusted)\n      return;\n    if (this._ignoreOverlayEvent(event))\n      return;\n    (_b = (_a = this._currentTool).onFocus) == null ? void 0 : _b.call(_a, event);\n  }\n  _onScroll(event) {\n    var _a, _b;\n    if (!event.isTrusted)\n      return;\n    this._lastHighlightedSelector = void 0;\n    this._lastHighlightedAriaTemplateJSON = "undefined";\n    this.highlight.hideActionPoint();\n    (_b = (_a = this._currentTool).onScroll) == null ? void 0 : _b.call(_a, event);\n  }\n  _onInput(event) {\n    var _a, _b;\n    if (this._ignoreOverlayEvent(event))\n      return;\n    (_b = (_a = this._currentTool).onInput) == null ? void 0 : _b.call(_a, event);\n  }\n  _onKeyDown(event) {\n    var _a, _b;\n    if (!event.isTrusted)\n      return;\n    if (this._ignoreOverlayEvent(event))\n      return;\n    (_b = (_a = this._currentTool).onKeyDown) == null ? void 0 : _b.call(_a, event);\n  }\n  _onKeyUp(event) {\n    var _a, _b;\n    if (!event.isTrusted)\n      return;\n    if (this._ignoreOverlayEvent(event))\n      return;\n    (_b = (_a = this._currentTool).onKeyUp) == null ? void 0 : _b.call(_a, event);\n  }\n  updateHighlight(model, userGesture) {\n    this._lastHighlightedSelector = void 0;\n    this._lastHighlightedAriaTemplateJSON = "undefined";\n    this._updateHighlight(model, userGesture);\n  }\n  _updateHighlight(model, userGesture) {\n    var _a, _b;\n    let tooltipText = model == null ? void 0 : model.tooltipText;\n    if (tooltipText === void 0 && (model == null ? void 0 : model.selector))\n      tooltipText = this.injectedScript.utils.asLocator(this.state.language, model.selector);\n    if (model)\n      this.highlight.updateHighlight(model.elements.map((element) => ({ element, color: model.color, tooltipText })));\n    else\n      this.highlight.clearHighlight();\n    if (userGesture)\n      (_b = (_a = this._delegate).highlightUpdated) == null ? void 0 : _b.call(_a);\n  }\n  _ignoreOverlayEvent(event) {\n    return event.composedPath().some((e) => {\n      const nodeName = e.nodeName || "";\n      return nodeName.toLowerCase() === "x-pw-glass";\n    });\n  }\n  deepEventTarget(event) {\n    var _a;\n    for (const element of event.composedPath()) {\n      if (!((_a = this.overlay) == null ? void 0 : _a.contains(element)))\n        return element;\n    }\n    return event.composedPath()[0];\n  }\n  setMode(mode) {\n    var _a, _b;\n    void ((_b = (_a = this._delegate).setMode) == null ? void 0 : _b.call(_a, mode));\n  }\n  async performAction(action) {\n    var _a, _b;\n    await ((_b = (_a = this._delegate).performAction) == null ? void 0 : _b.call(_a, action).catch(() => {\n    }));\n  }\n  recordAction(action) {\n    var _a, _b;\n    void ((_b = (_a = this._delegate).recordAction) == null ? void 0 : _b.call(_a, action));\n  }\n  setOverlayState(state) {\n    var _a, _b;\n    void ((_b = (_a = this._delegate).setOverlayState) == null ? void 0 : _b.call(_a, state));\n  }\n  elementPicked(selector, model) {\n    var _a, _b;\n    const ariaSnapshot = this.injectedScript.ariaSnapshot(model.elements[0]);\n    void ((_b = (_a = this._delegate).elementPicked) == null ? void 0 : _b.call(_a, { selector, ariaSnapshot }));\n  }\n};\nvar Dialog = class {\n  constructor(recorder) {\n    this._dialogElement = null;\n    this._recorder = recorder;\n  }\n  isShowing() {\n    return !!this._dialogElement;\n  }\n  show(options) {\n    const acceptButton = this._recorder.document.createElement("x-pw-tool-item");\n    acceptButton.title = "Accept";\n    acceptButton.classList.add("accept");\n    acceptButton.appendChild(this._recorder.document.createElement("x-div"));\n    acceptButton.addEventListener("click", () => options.onCommit());\n    const cancelButton = this._recorder.document.createElement("x-pw-tool-item");\n    cancelButton.title = "Close";\n    cancelButton.classList.add("cancel");\n    cancelButton.appendChild(this._recorder.document.createElement("x-div"));\n    cancelButton.addEventListener("click", () => {\n      var _a;\n      this.close();\n      (_a = options.onCancel) == null ? void 0 : _a.call(options);\n    });\n    this._dialogElement = this._recorder.document.createElement("x-pw-dialog");\n    this._keyboardListener = (event) => {\n      var _a;\n      if (event.key === "Escape") {\n        this.close();\n        (_a = options.onCancel) == null ? void 0 : _a.call(options);\n        return;\n      }\n      if (event.key === "Enter" && (event.ctrlKey || event.metaKey)) {\n        if (this._dialogElement)\n          options.onCommit();\n        return;\n      }\n    };\n    this._recorder.document.addEventListener("keydown", this._keyboardListener, true);\n    const toolbarElement = this._recorder.document.createElement("x-pw-tools-list");\n    const labelElement = this._recorder.document.createElement("label");\n    labelElement.textContent = options.label;\n    toolbarElement.appendChild(labelElement);\n    toolbarElement.appendChild(this._recorder.document.createElement("x-spacer"));\n    toolbarElement.appendChild(acceptButton);\n    toolbarElement.appendChild(cancelButton);\n    this._dialogElement.appendChild(toolbarElement);\n    const bodyElement = this._recorder.document.createElement("x-pw-dialog-body");\n    bodyElement.appendChild(options.body);\n    this._dialogElement.appendChild(bodyElement);\n    this._recorder.highlight.appendChild(this._dialogElement);\n    return this._dialogElement;\n  }\n  moveTo(top, left) {\n    if (!this._dialogElement)\n      return;\n    this._dialogElement.style.top = top + "px";\n    this._dialogElement.style.left = left + "px";\n  }\n  close() {\n    if (!this._dialogElement)\n      return;\n    this._dialogElement.remove();\n    this._recorder.document.removeEventListener("keydown", this._keyboardListener);\n    this._dialogElement = null;\n  }\n};\nfunction deepActiveElement(document) {\n  let activeElement = document.activeElement;\n  while (activeElement && activeElement.shadowRoot && activeElement.shadowRoot.activeElement)\n    activeElement = activeElement.shadowRoot.activeElement;\n  return activeElement;\n}\nfunction modifiersForEvent(event) {\n  return (event.altKey ? 1 : 0) | (event.ctrlKey ? 2 : 0) | (event.metaKey ? 4 : 0) | (event.shiftKey ? 8 : 0);\n}\nfunction buttonForEvent(event) {\n  switch (event.which) {\n    case 1:\n      return "left";\n    case 2:\n      return "middle";\n    case 3:\n      return "right";\n  }\n  return "left";\n}\nfunction positionForEvent(event) {\n  const targetElement = event.target;\n  if (targetElement.nodeName !== "CANVAS")\n    return;\n  return {\n    x: event.offsetX,\n    y: event.offsetY\n  };\n}\nfunction consumeEvent(e) {\n  e.preventDefault();\n  e.stopPropagation();\n  e.stopImmediatePropagation();\n}\nfunction asCheckbox(node) {\n  if (!node || node.nodeName !== "INPUT")\n    return null;\n  const inputElement = node;\n  return ["checkbox", "radio"].includes(inputElement.type) ? inputElement : null;\n}\nfunction isRangeInput(node) {\n  if (!node || node.nodeName !== "INPUT")\n    return false;\n  const inputElement = node;\n  return inputElement.type.toLowerCase() === "range";\n}\nfunction addEventListener(target, eventName, listener, useCapture) {\n  target.addEventListener(eventName, listener, useCapture);\n  const remove = () => {\n    target.removeEventListener(eventName, listener, useCapture);\n  };\n  return remove;\n}\nfunction removeEventListeners(listeners) {\n  for (const listener of listeners)\n    listener();\n  listeners.splice(0, listeners.length);\n}\nfunction entriesForSelectorHighlight(injectedScript, language, selector, ownerDocument) {\n  try {\n    const parsedSelector = injectedScript.parseSelector(selector);\n    const elements = injectedScript.querySelectorAll(parsedSelector, ownerDocument);\n    const color = elements.length > 1 ? HighlightColors.multiple : HighlightColors.single;\n    const locator = injectedScript.utils.asLocator(language, selector);\n    return elements.map((element, index) => {\n      const suffix = elements.length > 1 ? ` [${index + 1} of ${elements.length}]` : "";\n      return { element, color, tooltipText: locator + suffix };\n    });\n  } catch (e) {\n    return [];\n  }\n}\nfunction createSvgElement(doc, { tagName, attrs, children }) {\n  const elem = doc.createElementNS("http://www.w3.org/2000/svg", tagName);\n  if (attrs) {\n    for (const [k, v] of Object.entries(attrs))\n      elem.setAttribute(k, v);\n  }\n  if (children) {\n    for (const c of children)\n      elem.appendChild(createSvgElement(doc, c));\n  }\n  return elem;\n}\n\n// packages/injected/src/recorder/pollingRecorder.ts\nvar PollingRecorder = class {\n  constructor(injectedScript, options) {\n    this._recorder = new Recorder(injectedScript, options);\n    this._embedder = injectedScript.window;\n    injectedScript.onGlobalListenersRemoved.add(() => this._recorder.installListeners());\n    const refreshOverlay = () => {\n      this._lastStateJSON = void 0;\n      this._pollRecorderMode().catch((e) => console.log(e));\n    };\n    this._embedder.__pw_refreshOverlay = refreshOverlay;\n    refreshOverlay();\n  }\n  async _pollRecorderMode() {\n    const pollPeriod = 1e3;\n    if (this._pollRecorderModeTimer)\n      this._recorder.injectedScript.utils.builtins.clearTimeout(this._pollRecorderModeTimer);\n    const state = await this._embedder.__pw_recorderState().catch(() => null);\n    if (!state) {\n      this._pollRecorderModeTimer = this._recorder.injectedScript.utils.builtins.setTimeout(() => this._pollRecorderMode(), pollPeriod);\n      return;\n    }\n    const stringifiedState = JSON.stringify(state);\n    if (this._lastStateJSON !== stringifiedState) {\n      this._lastStateJSON = stringifiedState;\n      const win = this._recorder.document.defaultView;\n      if (win.top !== win) {\n        state.actionPoint = void 0;\n      }\n      this._recorder.setUIState(state, this);\n    }\n    this._pollRecorderModeTimer = this._recorder.injectedScript.utils.builtins.setTimeout(() => this._pollRecorderMode(), pollPeriod);\n  }\n  async performAction(action) {\n    await this._embedder.__pw_recorderPerformAction(action);\n  }\n  async recordAction(action) {\n    await this._embedder.__pw_recorderRecordAction(action);\n  }\n  async elementPicked(elementInfo) {\n    await this._embedder.__pw_recorderElementPicked(elementInfo);\n  }\n  async setMode(mode) {\n    await this._embedder.__pw_recorderSetMode(mode);\n  }\n  async setOverlayState(state) {\n    await this._embedder.__pw_recorderSetOverlayState(state);\n  }\n};\nvar pollingRecorder_default = PollingRecorder;\n';
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  source
});
