import { test, expect } from '@playwright/test';

test.describe('URL Fix Verification', () => {
  
  test('should navigate to Cricbuzz correctly', async ({ page }) => {
    console.log('🔍 Testing URL navigation...');
    
    // Navigate using relative URL (should use baseURL from config)
    await page.goto('/');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Verify we're on the right site
    const currentUrl = page.url();
    console.log(`Current URL: ${currentUrl}`);
    
    // Check that URL contains cricbuzz.com
    expect(currentUrl).toContain('cricbuzz.com');
    
    // Verify page title
    const title = await page.title();
    console.log(`Page title: ${title}`);
    expect(title).toContain('Cricbuzz');
    
    console.log('✅ URL navigation working correctly!');
  });
  
  test('should handle different URL patterns', async ({ page }) => {
    // Test different ways to navigate
    const urlsToTest = [
      '/',
      '/live-cricket-scores',
      '/cricket-news'
    ];
    
    for (const url of urlsToTest) {
      console.log(`Testing URL: ${url}`);
      
      try {
        await page.goto(url);
        await page.waitForLoadState('networkidle', { timeout: 10000 });
        
        const currentUrl = page.url();
        console.log(`✅ Successfully navigated to: ${currentUrl}`);
        
        // Verify we're still on cricbuzz.com
        expect(currentUrl).toContain('cricbuzz.com');
        
      } catch (error) {
        console.log(`❌ Failed to navigate to ${url}: ${error.message}`);
        throw error;
      }
    }
  });
  
  test('should work with absolute URLs too', async ({ page }) => {
    // Test that absolute URLs still work
    await page.goto('https://www.cricbuzz.com');
    await page.waitForLoadState('networkidle');
    
    const currentUrl = page.url();
    console.log(`Absolute URL navigation: ${currentUrl}`);
    
    expect(currentUrl).toContain('cricbuzz.com');
    
    const title = await page.title();
    expect(title).toContain('Cricbuzz');
    
    console.log('✅ Absolute URL navigation also working!');
  });
});
