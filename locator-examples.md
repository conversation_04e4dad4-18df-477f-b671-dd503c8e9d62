# Cricbuzz Locator Discovery Process

## How I Found the Locators in CricbuzzPage.ts

### 1. **Navigation Menu Locators**
```typescript
this.liveScoresTab = page.locator('a[title="Live Cricket Score"]');
this.newsTab = page.locator('a[title="Cricket News"]');
```

**Discovery Process:**
1. Visited https://www.cricbuzz.com
2. Right-clicked on "Live Scores" link
3. Inspected HTML: `<a title="Live Cricket Score" href="/live-cricket-scores">Live Scores</a>`
4. Chose `title` attribute as it's unique and descriptive

### 2. **Logo Locator**
```typescript
this.logo = page.locator('[title="Cricbuzz Logo"]');
```

**Discovery Process:**
1. Inspected the Cricbuzz logo
2. Found: `<img title="Cricbuzz Logo" src="logo.png" alt="Cricbuzz">`
3. Used title attribute for reliability

### 3. **Live Match Cards**
```typescript
this.liveMatchCards = page.locator('.cb-mtch-lst');
this.teamNames = page.locator('.cb-ovr-flo h3');
```

**Discovery Process:**
1. Went to Live Scores section
2. Inspected match cards structure
3. Found pattern: Each match has class `cb-mtch-lst`
4. Team names are in `h3` tags within `.cb-ovr-flo` containers

### 4. **News Headlines**
```typescript
this.newsHeadlines = page.locator('.cb-nws-hdln');
```

**Discovery Process:**
1. Navigated to News section
2. Right-clicked on news headlines
3. Found consistent class pattern: `cb-nws-hdln`

## Common Locator Strategies

### 1. **ID Selectors (Most Reliable)**
```typescript
page.locator('#search-box')           // HTML: <input id="search-box">
page.locator('#main-content')         // HTML: <div id="main-content">
```

### 2. **Class Selectors**
```typescript
page.locator('.cb-nav-main')          // HTML: <nav class="cb-nav-main">
page.locator('.match-card')           // HTML: <div class="match-card">
```

### 3. **Attribute Selectors**
```typescript
page.locator('[data-testid="login-btn"]')     // HTML: <button data-testid="login-btn">
page.locator('[title="Live Cricket Score"]')  // HTML: <a title="Live Cricket Score">
page.locator('[placeholder="Search"]')        // HTML: <input placeholder="Search">
```

### 4. **Text-based Selectors**
```typescript
page.locator('text=Live Scores')              // Finds element containing "Live Scores"
page.locator('button:has-text("Submit")')     // Button with "Submit" text
page.getByRole('button', { name: 'Login' })   // Semantic role-based
```

### 5. **CSS Selectors**
```typescript
page.locator('nav > ul > li')                 // Direct child selectors
page.locator('.header .search-box input')     // Descendant selectors
page.locator('a[href*="live-scores"]')        // Attribute contains
```

### 6. **XPath Selectors**
```typescript
page.locator('//button[contains(text(), "Submit")]')
page.locator('//div[@class="match-card"][1]')        // First match card
page.locator('//a[starts-with(@href, "/news")]')     // News links
```

## Tools for Locator Discovery

### 1. **Browser DevTools**
- **Chrome**: F12 → Elements tab
- **Firefox**: F12 → Inspector tab
- **Edge**: F12 → Elements tab

### 2. **Playwright Tools**
```bash
# Interactive codegen
npx playwright codegen https://www.cricbuzz.com

# Debug mode
npx playwright test --debug

# Trace viewer
npx playwright test --trace on
npx playwright show-trace trace.zip
```

### 3. **Browser Extensions**
- **ChroPath** (Chrome) - XPath and CSS selector helper
- **SelectorsHub** - Multi-selector generator
- **Playwright Test Generator** - Records actions

## Best Practices for Locators

### 1. **Priority Order (Most to Least Reliable)**
1. `data-testid` attributes (if available)
2. `id` attributes
3. Semantic roles (`getByRole`)
4. `name` attributes
5. `class` attributes (stable ones)
6. Text content
7. CSS selectors
8. XPath (last resort)

### 2. **Avoid These Locators**
```typescript
// ❌ Bad - Too generic
page.locator('div')
page.locator('.btn')

// ❌ Bad - Position-dependent
page.locator('div:nth-child(3)')
page.locator('tr:first-child')

// ❌ Bad - Dynamic classes
page.locator('.css-1a2b3c4')
page.locator('.MuiButton-root-123')
```

### 3. **Good Locator Examples**
```typescript
// ✅ Good - Specific and stable
page.locator('[data-testid="submit-button"]')
page.locator('#user-email')
page.getByRole('button', { name: 'Submit' })
page.locator('.search-input[placeholder="Search"]')
```

## Cricbuzz-Specific Patterns

### Class Naming Convention
Cricbuzz uses `cb-` prefix for their CSS classes:
- `cb-nav-main` - Main navigation
- `cb-mtch-lst` - Match list items
- `cb-nws-hdln` - News headlines
- `cb-scr-wll-chvrn` - Score display

### Common Selectors for Cricbuzz
```typescript
// Navigation
page.locator('.cb-nav-main a[title*="Live"]')

// Matches
page.locator('.cb-mtch-lst .cb-ovr-flo')

// News
page.locator('.cb-nws-hdln a')

// Scores
page.locator('.cb-scr-wll-chvrn .cb-font-20')
```

## Testing Your Locators

### In Browser Console
```javascript
// Test CSS selectors
document.querySelector('.cb-nav-main')
document.querySelectorAll('.cb-mtch-lst')

// Test XPath
$x('//a[contains(@title, "Live")]')
```

### In Playwright
```typescript
// Check if element exists
await expect(page.locator('.cb-nav-main')).toBeVisible()

// Count elements
const count = await page.locator('.cb-mtch-lst').count()

// Get text content
const text = await page.locator('.cb-nws-hdln').first().textContent()
```
