import { Page, Locator } from '@playwright/test';

export class EpiqPage {
  readonly page: Page;

  // Login elements (from Inspector)
  readonly userIdField: Locator;
  readonly passwordField: Locator;
  readonly loginButton: Locator;

  // Common elements (update these with actual locators)
  readonly fetchRecordsButton: Locator;
  readonly documentCategoriesSection: Locator;
  readonly availableDocumentsSection: Locator;
  readonly selectedDocumentsSection: Locator;
  readonly searchInput: Locator;
  readonly topicUniqueCode: Locator;
  readonly descriptionField: Locator;
  
  // Document list elements
  readonly documentList: Locator;
  readonly documentItems: Locator;
  
  constructor(page: Page) {
    this.page = page;
    
    // Initialize locators (update these based on actual inspection)
    // These are common patterns - replace with actual locators from inspection
    this.fetchRecordsButton = page.locator('button:has-text("Fetch Records"), input[value="Fetch Records"], [onclick*="fetch"]');
    this.documentCategoriesSection = page.locator('.document-categories, #documentCategories');
    this.availableDocumentsSection = page.locator('.available-documents, #availableDocuments');
    this.selectedDocumentsSection = page.locator('.selected-documents, #selectedDocuments');
    this.searchInput = page.locator('input[type="search"], input[placeholder*="Search"]');
    this.topicUniqueCode = page.locator('input[placeholder*="Topic"], #topicCode');
    this.descriptionField = page.locator('textarea[placeholder*="Description"], #description');
    
    // Document list locators
    this.documentList = page.locator('.document-list, .doc-list');
    this.documentItems = page.locator('.document-item, .doc-item, tr[data-document]');
  }

  /**
   * Navigate to EPIQ application
   */
  async navigate(): Promise<void> {
    await this.page.goto('/');
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Click Fetch Records button
   */
  async clickFetchRecords(): Promise<void> {
    await this.fetchRecordsButton.click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Search for documents
   */
  async searchDocuments(searchTerm: string): Promise<void> {
    await this.searchInput.fill(searchTerm);
    await this.searchInput.press('Enter');
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Set topic unique code
   */
  async setTopicUniqueCode(code: string): Promise<void> {
    await this.topicUniqueCode.fill(code);
  }

  /**
   * Set description
   */
  async setDescription(description: string): Promise<void> {
    await this.descriptionField.fill(description);
  }

  /**
   * Get list of available documents
   */
  async getAvailableDocuments(): Promise<string[]> {
    await this.documentItems.first().waitFor({ timeout: 10000 });
    return await this.documentItems.allTextContents();
  }

  /**
   * Select a document by name
   */
  async selectDocument(documentName: string): Promise<void> {
    const document = this.page.locator(`text=${documentName}`);
    await document.click();
  }

  /**
   * Get selected documents count
   */
  async getSelectedDocumentsCount(): Promise<number> {
    const selectedSection = this.selectedDocumentsSection;
    const documents = selectedSection.locator('.document-item, tr, .doc-item');
    return await documents.count();
  }

  /**
   * Check if Fetch Records button is enabled
   */
  async isFetchRecordsEnabled(): Promise<boolean> {
    return await this.fetchRecordsButton.isEnabled();
  }

  /**
   * Check if Fetch Records button is visible
   */
  async isFetchRecordsVisible(): Promise<boolean> {
    return await this.fetchRecordsButton.isVisible();
  }

  /**
   * Wait for documents to load
   */
  async waitForDocumentsToLoad(): Promise<void> {
    await this.documentItems.first().waitFor({ timeout: 30000 });
  }

  /**
   * Get current page title
   */
  async getTitle(): Promise<string> {
    return await this.page.title();
  }

  /**
   * Get current URL
   */
  async getCurrentUrl(): Promise<string> {
    return this.page.url();
  }

  /**
   * Take screenshot for debugging
   */
  async takeScreenshot(name: string): Promise<void> {
    await this.page.screenshot({ 
      path: `screenshots/epiq-${name}-${Date.now()}.png`,
      fullPage: true 
    });
  }
}
