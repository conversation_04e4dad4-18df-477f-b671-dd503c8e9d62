// Simple script to find "Fetch Records" button without copying from Inspector
const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  try {
    // Navigate to EPIQ
    console.log('🔍 Navigating to EPIQ application...');
    await page.goto('https://cqmprdtstapp01.aurelius.com:561/epiqByCaliber_310V_SP_TST/');
    await page.waitForLoadState('networkidle');
    
    console.log('✅ Page loaded. Searching for "Fetch Records" button...\n');
    
    // List of possible selectors for "Fetch Records" button
    const selectors = [
      // Text-based selectors (most reliable)
      'button:has-text("Fetch Records")',
      'input[value="Fetch Records"]',
      '*:has-text("Fetch Records")',
      
      // Common ID patterns
      '#fetchRecords',
      '#fetchBtn',
      '#fetch',
      '#btnFetch',
      
      // Common class patterns
      '.fetch-records',
      '.fetch-btn',
      '.btn-fetch',
      '.fetchRecords',
      
      // Attribute-based
      '[onclick*="fetch"]',
      '[onclick*="Fetch"]',
      '[value*="Fetch"]',
      '[title*="Fetch"]',
      
      // Generic button patterns
      'button[type="button"]',
      'input[type="button"]',
      'input[type="submit"]',
      
      // Role-based
      '[role="button"]'
    ];
    
    const foundSelectors = [];
    
    // Test each selector
    for (let i = 0; i < selectors.length; i++) {
      const selector = selectors[i];
      try {
        const elements = await page.locator(selector).all();
        
        for (let j = 0; j < elements.length; j++) {
          const element = elements[j];
          const isVisible = await element.isVisible();
          const text = await element.textContent();
          const tagName = await element.evaluate(el => el.tagName);
          
          // Check if this might be our "Fetch Records" button
          if (text && (
            text.toLowerCase().includes('fetch') ||
            text.toLowerCase().includes('records') ||
            text.toLowerCase().includes('search') ||
            text.toLowerCase().includes('get')
          )) {
            foundSelectors.push({
              selector: selector,
              text: text.trim(),
              visible: isVisible,
              tagName: tagName,
              index: j
            });
          }
        }
      } catch (error) {
        // Selector failed, continue
      }
    }
    
    // Display results
    if (foundSelectors.length > 0) {
      console.log('🎯 FOUND POTENTIAL "FETCH RECORDS" BUTTONS:\n');
      console.log('=' .repeat(60));
      
      foundSelectors.forEach((item, index) => {
        console.log(`${index + 1}. Selector: ${item.selector}`);
        console.log(`   Text: "${item.text}"`);
        console.log(`   Tag: ${item.tagName}`);
        console.log(`   Visible: ${item.visible}`);
        console.log(`   Index: ${item.index}`);
        console.log('-'.repeat(40));
      });
      
      console.log('\n📋 COPY THESE SELECTORS TO USE IN YOUR TESTS:');
      console.log('=' .repeat(50));
      
      // Show the most likely candidates
      const likelyButtons = foundSelectors.filter(item => 
        item.text.toLowerCase().includes('fetch') && item.visible
      );
      
      if (likelyButtons.length > 0) {
        console.log('\n🎯 MOST LIKELY "FETCH RECORDS" BUTTONS:');
        likelyButtons.forEach((item, index) => {
          console.log(`\n${index + 1}. page.locator('${item.selector}')`);
          if (item.index > 0) {
            console.log(`   Or: page.locator('${item.selector}').nth(${item.index})`);
          }
          console.log(`   Text: "${item.text}"`);
        });
      }
      
      // Provide ready-to-use code
      console.log('\n📝 READY-TO-USE CODE:');
      console.log('=' .repeat(30));
      console.log('// Add this to your EpiqPage.ts:');
      
      if (likelyButtons.length > 0) {
        const bestButton = likelyButtons[0];
        console.log(`this.fetchRecordsButton = page.locator('${bestButton.selector}');`);
        
        console.log('\n// Test it with:');
        console.log('await epiqPage.fetchRecordsButton.click();');
      } else {
        console.log('// Try these selectors:');
        foundSelectors.slice(0, 3).forEach(item => {
          console.log(`// this.fetchRecordsButton = page.locator('${item.selector}');`);
        });
      }
      
    } else {
      console.log('❌ No potential "Fetch Records" buttons found.');
      console.log('\n🔧 TROUBLESHOOTING:');
      console.log('1. Make sure you are logged in');
      console.log('2. Navigate to the correct page');
      console.log('3. Check if the button is in an iframe');
      console.log('4. The button might have different text');
      
      // Show all buttons on the page
      console.log('\n📋 ALL BUTTONS ON THE PAGE:');
      const allButtons = await page.locator('button, input[type="button"], input[type="submit"]').all();
      
      for (let i = 0; i < Math.min(allButtons.length, 10); i++) {
        const button = allButtons[i];
        const text = await button.textContent();
        const value = await button.getAttribute('value');
        const isVisible = await button.isVisible();
        
        console.log(`${i + 1}. Text: "${text || value || 'No text'}" (Visible: ${isVisible})`);
      }
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    console.log('\n🔧 Possible issues:');
    console.log('1. Network connection problem');
    console.log('2. Authentication required');
    console.log('3. URL might be incorrect');
    console.log('4. Page might be loading slowly');
  }
  
  console.log('\n⏸️  Browser will stay open for 30 seconds for manual inspection...');
  await page.waitForTimeout(30000);
  
  await browser.close();
})();
