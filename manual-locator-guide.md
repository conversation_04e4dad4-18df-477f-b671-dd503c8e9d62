# Manual Locator Discovery Guide

## 🎯 Step-by-Step Browser DevTools Method

### Step 1: Open DevTools
1. Open your EPIQ application: https://cqmprdtstapp01.aurelius.com:561/epiqByCaliber_310V_SP_TST/
2. Press **F12** or **Ctrl+Shift+I** to open DevTools
3. Click the **Elements** tab

### Step 2: Inspect the "Fetch Records" Button
1. Press **Ctrl+Shift+C** (or click the inspect icon 🔍)
2. Click on the "Fetch Records" button
3. The HTML element will be highlighted in DevTools

### Step 3: Copy the Locator
1. **Right-click** on the highlighted HTML element in DevTools
2. Select **"Copy"** from the context menu
3. Choose one of these options:
   - **Copy selector** (CSS selector)
   - **Copy XPath** (XPath selector)
   - **Copy element** (full HTML)

### Step 4: Common HTML Patterns to Look For

The "Fetch Records" button might look like:

```html
<!-- Button element -->
<button id="fetchBtn" class="btn-primary">Fetch Records</button>

<!-- Input button -->
<input type="button" value="Fetch Records" onclick="fetchRecords()">

<!-- Link with onclick -->
<a href="#" onclick="fetchRecords()" class="fetch-link">Fetch Records</a>

<!-- Div with click handler -->
<div class="fetch-records-btn" data-action="fetch">Fetch Records</div>
```

### Step 5: Create Locators from HTML

Based on the HTML you see, create these locators:

```typescript
// If you see: <button id="fetchBtn">
page.locator('#fetchBtn')

// If you see: <input value="Fetch Records">
page.locator('input[value="Fetch Records"]')

// If you see: class="fetch-records"
page.locator('.fetch-records')

// If you see: onclick="fetchRecords()"
page.locator('[onclick*="fetchRecords"]')

// Generic text-based (usually works)
page.getByText('Fetch Records')
page.locator('button:has-text("Fetch Records")')
```

## 🔍 Alternative Discovery Methods

### Method A: Browser Console
1. Open DevTools → **Console** tab
2. Type these commands to test selectors:

```javascript
// Test if element exists
document.querySelector('button:contains("Fetch Records")')

// Find by text content
Array.from(document.querySelectorAll('*')).find(el => el.textContent?.includes('Fetch Records'))

// Find buttons
document.querySelectorAll('button, input[type="button"], input[type="submit"]')

// Find by onclick attribute
document.querySelectorAll('[onclick*="fetch"]')
```

### Method B: View Page Source
1. Press **Ctrl+U** to view page source
2. Press **Ctrl+F** to search
3. Search for: "Fetch Records"
4. Look at the surrounding HTML

### Method C: Network Tab Method
1. Open DevTools → **Network** tab
2. Click "Fetch Records" button
3. Look at the network requests to understand what happens
4. Use this info to find the button

## 📝 Manual Locator Testing

### Test Your Locators in Console:
```javascript
// Test CSS selector
document.querySelector('YOUR_SELECTOR_HERE')

// Test XPath
document.evaluate('YOUR_XPATH_HERE', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue

// Count matching elements
document.querySelectorAll('YOUR_SELECTOR_HERE').length
```

## 🎯 Common EPIQ Application Patterns

Based on typical enterprise applications, try these selectors:

```typescript
// Common button patterns
page.locator('button:has-text("Fetch Records")')
page.locator('input[value="Fetch Records"]')
page.locator('[onclick*="fetch"]')
page.locator('.fetch, .fetch-btn, .fetch-records')
page.locator('#fetch, #fetchBtn, #fetchRecords')

// Generic patterns
page.getByRole('button', { name: /fetch/i })
page.getByText('Fetch Records')
page.locator('*:has-text("Fetch Records")')
```

## 🚨 Troubleshooting Tips

### If Element is in an iframe:
```typescript
// Switch to iframe first
const frame = page.frameLocator('iframe[name="mainFrame"]')
const button = frame.locator('button:has-text("Fetch Records")')
```

### If Element is Dynamic:
```typescript
// Wait for element to appear
await page.waitForSelector('button:has-text("Fetch Records")')

// Use more flexible selectors
page.locator('button', { hasText: 'Fetch' })
```

### If Multiple Elements Match:
```typescript
// Use more specific selectors
page.locator('.document-section button:has-text("Fetch Records")')

// Or use nth element
page.locator('button:has-text("Fetch Records")').nth(0)
```
