// EPIQ Application Locator Discovery Script
const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // Navigate to your EPIQ application
  const epiqUrl = 'https://cqmprdtstapp01.aurelius.com:561/epiqByCaliber_310V_SP_TST/';
  
  console.log('🔍 EPIQ Locator Discovery');
  console.log('========================');
  console.log('1. Navigate to your EPIQ application');
  console.log('2. Use Inspector to find "Fetch Records" button');
  console.log('3. Try different locator strategies');
  
  try {
    await page.goto(epiqUrl);
    console.log('✅ Navigated to EPIQ application');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Look for common "Fetch Records" button patterns
    const possibleSelectors = [
      'button:has-text("Fetch Records")',
      'input[value="Fetch Records"]',
      '[onclick*="fetch"]',
      '.fetch-records',
      '#fetchRecords',
      'button[title*="Fetch"]',
      'a:has-text("Fetch Records")'
    ];
    
    console.log('\n🎯 Testing common "Fetch Records" selectors:');
    
    for (const selector of possibleSelectors) {
      try {
        const element = page.locator(selector);
        const count = await element.count();
        if (count > 0) {
          const isVisible = await element.first().isVisible();
          console.log(`✅ Found: ${selector} (${count} elements, ${isVisible ? 'visible' : 'hidden'})`);
        }
      } catch (error) {
        // Selector not found, continue
      }
    }
    
    console.log('\n📋 Use Inspector to find exact locators:');
    console.log('1. Click the target icon in Inspector');
    console.log('2. Click on "Fetch Records" button');
    console.log('3. Copy the generated locator');
    
    // Pause for manual inspection
    await page.pause();
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    console.log('You may need to login first or check the URL');
  }
  
  await browser.close();
})();
