import { test, expect } from '@playwright/test';
import { CricbuzzPage } from '../src/pages/CricbuzzPage';

test.describe('Cricbuzz Website Tests', () => {
  let cricbuzzPage: CricbuzzPage;

  test.beforeEach(async ({ page }) => {
    cricbuzzPage = new CricbuzzPage(page);
    await cricbuzzPage.navigate();
  });

  test('should load Cricbuzz homepage successfully', async () => {
    // Verify page title
    const title = await cricbuzzPage.getTitle();
    expect(title).toContain('Cricbuzz');
    
    // Verify logo is visible
    const isLogoVisible = await cricbuzzPage.isLogoVisible();
    expect(isLogoVisible).toBe(true);
    
    // Verify URL
    const currentUrl = await cricbuzzPage.getCurrentUrl();
    expect(currentUrl).toContain('cricbuzz.com');
  });

  test('should navigate to different sections', async () => {
    // Test Live Scores navigation
    await cricbuzzPage.clickLiveScores();
    let currentUrl = await cricbuzzPage.getCurrentUrl();
    expect(currentUrl).toContain('live-cricket-scores');
    
    // Test News navigation
    await cricbuzzPage.clickNews();
    currentUrl = await cricbuzzPage.getCurrentUrl();
    expect(currentUrl).toContain('cricket-news');
    
    // Test Schedule navigation
    await cricbuzzPage.clickSchedule();
    currentUrl = await cricbuzzPage.getCurrentUrl();
    expect(currentUrl).toContain('cricket-schedule');
  });

  test('should display live matches information', async () => {
    await cricbuzzPage.clickLiveScores();
    
    // Get live matches data
    const liveMatches = await cricbuzzPage.getLiveMatches();
    
    // Verify that we get some match data (even if no live matches, structure should be correct)
    expect(Array.isArray(liveMatches)).toBe(true);
    
    // If there are live matches, verify the structure
    if (liveMatches.length > 0) {
      const firstMatch = liveMatches[0];
      expect(firstMatch).toHaveProperty('team1');
      expect(firstMatch).toHaveProperty('team2');
      expect(firstMatch).toHaveProperty('status');
      expect(firstMatch).toHaveProperty('score');
      
      console.log('Live Matches Found:', liveMatches);
    } else {
      console.log('No live matches currently available');
    }
  });

  test('should display news headlines', async () => {
    await cricbuzzPage.clickNews();
    
    // Get news headlines
    const headlines = await cricbuzzPage.getNewsHeadlines();
    
    // Verify that we get news headlines
    expect(headlines.length).toBeGreaterThan(0);
    expect(headlines[0]).toBeTruthy();
    
    console.log('Latest News Headlines:', headlines.slice(0, 5)); // Log first 5 headlines
  });

  test('should be able to search for cricket content', async ({ page }) => {
    // Search for a popular cricket team
    await cricbuzzPage.search('India cricket');
    
    // Wait for search results
    await page.waitForLoadState('networkidle');
    
    // Verify we're on search results page
    const currentUrl = await cricbuzzPage.getCurrentUrl();
    expect(currentUrl).toContain('search');
  });

  test('should click on a news article', async ({ page }) => {
    await cricbuzzPage.clickNews();
    
    // Get initial URL
    const initialUrl = await cricbuzzPage.getCurrentUrl();
    
    // Click on first news article
    await cricbuzzPage.clickNewsArticle(0);
    
    // Verify navigation to article page
    const newUrl = await cricbuzzPage.getCurrentUrl();
    expect(newUrl).not.toBe(initialUrl);
  });

  test('should verify page responsiveness', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await cricbuzzPage.navigate();
    
    const isLogoVisible = await cricbuzzPage.isLogoVisible();
    expect(isLogoVisible).toBe(true);
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await cricbuzzPage.navigate();
    
    const isLogoVisibleTablet = await cricbuzzPage.isLogoVisible();
    expect(isLogoVisibleTablet).toBe(true);
  });
});

test.describe('Cricbuzz Performance Tests', () => {
  test('should load homepage within acceptable time', async ({ page }) => {
    const cricbuzzPage = new CricbuzzPage(page);
    
    const startTime = Date.now();
    await cricbuzzPage.navigate();
    const endTime = Date.now();
    
    const loadTime = endTime - startTime;
    console.log(`Page load time: ${loadTime}ms`);
    
    // Expect page to load within 10 seconds
    expect(loadTime).toBeLessThan(10000);
  });
});
