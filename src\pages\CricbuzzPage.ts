import { Page, Locator } from '@playwright/test';

export class CricbuzzPage {
  readonly page: Page;
  
  // Header elements
  readonly logo: Locator;
  readonly searchBox: Locator;
  readonly menuItems: Locator;
  
  // Navigation menu
  readonly liveScoresTab: Locator;
  readonly scheduleTab: Locator;
  readonly newsTab: Locator;
  readonly seriesTab: Locator;
  readonly teamsTab: Locator;
  readonly videosTab: Locator;
  readonly rankingsTab: Locator;
  readonly moreTab: Locator;
  
  // Live scores section
  readonly liveMatchCards: Locator;
  readonly matchStatus: Locator;
  readonly teamNames: Locator;
  readonly scores: Locator;
  
  // News section
  readonly newsHeadlines: Locator;
  readonly newsImages: Locator;
  readonly newsCategories: Locator;
  
  constructor(page: Page) {
    this.page = page;
    
    // Initialize header locators
    this.logo = page.locator('[title="Cricbuzz Logo"]');
    this.searchBox = page.locator('#search-box');
    this.menuItems = page.locator('.cb-nav-main');
    
    // Initialize navigation menu locators
    this.liveScoresTab = page.locator('a[title="Live Cricket Score"]');
    this.scheduleTab = page.locator('a[title="Cricket Schedule"]');
    this.newsTab = page.locator('a[title="Cricket News"]');
    this.seriesTab = page.locator('a[title="Cricket Series"]');
    this.teamsTab = page.locator('a[title="Cricket Teams"]');
    this.videosTab = page.locator('a[title="Cricket Videos"]');
    this.rankingsTab = page.locator('a[title="Cricket Rankings"]');
    this.moreTab = page.locator('a[title="More"]');
    
    // Initialize live scores locators
    this.liveMatchCards = page.locator('.cb-mtch-lst');
    this.matchStatus = page.locator('.cb-text-live, .cb-text-complete');
    this.teamNames = page.locator('.cb-ovr-flo h3');
    this.scores = page.locator('.cb-ovr-flo .cb-scr-wll-chvrn');
    
    // Initialize news locators
    this.newsHeadlines = page.locator('.cb-nws-hdln');
    this.newsImages = page.locator('.cb-nws-img');
    this.newsCategories = page.locator('.cb-nws-cat');
  }

  /**
   * Navigate to Cricbuzz homepage
   */
  async navigate(): Promise<void> {
    await this.page.goto('https://www.cricbuzz.com');
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Get page title
   */
  async getTitle(): Promise<string> {
    return await this.page.title();
  }

  /**
   * Search for cricket content
   */
  async search(searchTerm: string): Promise<void> {
    await this.searchBox.fill(searchTerm);
    await this.searchBox.press('Enter');
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Click on Live Scores tab
   */
  async clickLiveScores(): Promise<void> {
    await this.liveScoresTab.click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Click on News tab
   */
  async clickNews(): Promise<void> {
    await this.newsTab.click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Click on Schedule tab
   */
  async clickSchedule(): Promise<void> {
    await this.scheduleTab.click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Get all live match information
   */
  async getLiveMatches(): Promise<Array<{team1: string, team2: string, status: string, score: string}>> {
    const matches = [];
    const matchCards = await this.liveMatchCards.all();
    
    for (const card of matchCards) {
      const teams = await card.locator('.cb-ovr-flo h3').allTextContents();
      const status = await card.locator('.cb-text-live, .cb-text-complete').first().textContent();
      const score = await card.locator('.cb-ovr-flo .cb-scr-wll-chvrn').first().textContent();
      
      if (teams.length >= 2) {
        matches.push({
          team1: teams[0]?.trim() || '',
          team2: teams[1]?.trim() || '',
          status: status?.trim() || '',
          score: score?.trim() || ''
        });
      }
    }
    
    return matches;
  }

  /**
   * Get latest news headlines
   */
  async getNewsHeadlines(): Promise<string[]> {
    await this.newsHeadlines.first().waitFor({ timeout: 10000 });
    return await this.newsHeadlines.allTextContents();
  }

  /**
   * Click on a specific news article by index
   */
  async clickNewsArticle(index: number): Promise<void> {
    const headlines = await this.newsHeadlines.all();
    if (headlines[index]) {
      await headlines[index].click();
      await this.page.waitForLoadState('networkidle');
    }
  }

  /**
   * Check if logo is visible
   */
  async isLogoVisible(): Promise<boolean> {
    return await this.logo.isVisible();
  }

  /**
   * Get current URL
   */
  async getCurrentUrl(): Promise<string> {
    return this.page.url();
  }

  /**
   * Wait for page to load completely
   */
  async waitForPageLoad(): Promise<void> {
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForSelector('.cb-nav-main', { timeout: 10000 });
  }
}
