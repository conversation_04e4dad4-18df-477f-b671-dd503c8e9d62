import { test, expect } from '@playwright/test';
import { EpiqPage } from '../src/pages/EpiqPage';

test.describe('EPIQ Fetch Records Tests', () => {
  let epiqPage: EpiqPage;

  test.beforeEach(async ({ page }) => {
    epiqPage = new EpiqPage(page);
    // You may need to handle login here
    await epiqPage.navigate();
  });

  test('should find and interact with Fetch Records button', async ({ page }) => {
    console.log('🔍 Looking for Fetch Records button...');
    
    // Check if Fetch Records button is visible
    const isVisible = await epiqPage.isFetchRecordsVisible();
    console.log(`Fetch Records button visible: ${isVisible}`);
    
    if (isVisible) {
      // Check if it's enabled
      const isEnabled = await epiqPage.isFetchRecordsEnabled();
      console.log(`Fetch Records button enabled: ${isEnabled}`);
      
      if (isEnabled) {
        // Click the button
        await epiqPage.clickFetchRecords();
        console.log('✅ Successfully clicked Fetch Records button');
        
        // Wait for results to load
        await epiqPage.waitForDocumentsToLoad();
        
        // Get document count
        const documents = await epiqPage.getAvailableDocuments();
        console.log(`Found ${documents.length} documents`);
        
      } else {
        console.log('❌ Fetch Records button is disabled');
      }
    } else {
      console.log('❌ Fetch Records button not found');
      
      // Take screenshot for debugging
      await epiqPage.takeScreenshot('fetch-records-not-found');
      
      // Try to find alternative selectors
      await debugFetchRecordsLocators(page);
    }
  });

  test('should handle search and fetch workflow', async ({ page }) => {
    // Set topic unique code
    await epiqPage.setTopicUniqueCode('SP31');
    
    // Set description
    await epiqPage.setDescription('Test automation');
    
    // Search for documents
    await epiqPage.searchDocuments('DOC%');
    
    // Click fetch records
    await epiqPage.clickFetchRecords();
    
    // Verify documents are loaded
    const documentsCount = await epiqPage.getSelectedDocumentsCount();
    expect(documentsCount).toBeGreaterThan(0);
    
    console.log(`✅ Workflow completed with ${documentsCount} documents`);
  });

  // Helper method to debug locators
  async function debugFetchRecordsLocators(page: any) {
    console.log('\n🔧 Debugging Fetch Records locators...');
    
    const possibleSelectors = [
      'button:has-text("Fetch Records")',
      'input[value="Fetch Records"]',
      '[onclick*="fetch"]',
      '.fetch-records',
      '#fetchRecords',
      'button[title*="Fetch"]',
      'a:has-text("Fetch Records")',
      '[value*="Fetch"]',
      'button:has-text("Fetch")',
      'input[type="button"][value*="Fetch"]'
    ];
    
    for (const selector of possibleSelectors) {
      try {
        const element = page.locator(selector);
        const count = await element.count();
        if (count > 0) {
          const isVisible = await element.first().isVisible();
          const text = await element.first().textContent();
          console.log(`✅ Found: ${selector}`);
          console.log(`   Count: ${count}, Visible: ${isVisible}, Text: "${text}"`);
        }
      } catch (error) {
        // Continue to next selector
      }
    }
  }
});
