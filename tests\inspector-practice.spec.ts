import { test, expect } from '@playwright/test';

test.describe('Playwright Inspector Practice', () => {
  
  test('Interactive locator discovery with Inspector', async ({ page }) => {
    // Navigate to Cricbuzz
    await page.goto('https://www.cricbuzz.com');
    
    console.log('🔍 PLAYWRIGHT INSPECTOR TUTORIAL');
    console.log('================================');
    console.log('The browser will pause here so you can:');
    console.log('1. Use the Inspector to explore elements');
    console.log('2. Click the "Pick locator" button (target icon)');
    console.log('3. Click on any element to see its locator');
    console.log('4. Test different locator strategies');
    console.log('5. Copy the locators for your tests');
    console.log('');
    console.log('Try finding locators for:');
    console.log('- Logo');
    console.log('- Live Scores link');
    console.log('- Search box');
    console.log('- News headlines');
    console.log('- Match cards');
    console.log('');
    console.log('Click the "Resume" button when done exploring!');
    
    // This will pause and open the Playwright Inspector
    await page.pause();
    
    // After you resume, let's test some locators you might have found
    console.log('✅ Great! Now let\'s test some common locators...');
    
    // Test if logo is visible (you can modify this based on what you found)
    const logo = page.locator('img[alt*="Cricbuzz"], [title*="Cricbuzz"]');
    if (await logo.first().isVisible()) {
      console.log('✅ Logo found and visible');
    }
    
    // Test navigation elements
    const navigation = page.locator('nav, .nav, .navigation, .cb-nav-main');
    if (await navigation.first().isVisible()) {
      console.log('✅ Navigation found');
    }
  });
  
  test('Step-by-step locator testing', async ({ page }) => {
    await page.goto('https://www.cricbuzz.com');
    
    console.log('🎯 STEP-BY-STEP LOCATOR PRACTICE');
    console.log('=================================');
    
    // Step 1: Find the logo
    console.log('Step 1: Finding the logo...');
    await page.pause(); // Pause to let you find the logo locator
    
    // Step 2: Find navigation menu
    console.log('Step 2: Finding navigation menu...');
    await page.pause(); // Pause to let you find navigation
    
    // Step 3: Find Live Scores link
    console.log('Step 3: Finding Live Scores link...');
    await page.pause(); // Pause to let you find Live Scores
    
    // Step 4: Click Live Scores and find match cards
    console.log('Step 4: Click Live Scores and find match cards...');
    // You can click Live Scores in the inspector and then find match elements
    await page.pause();
    
    console.log('🎉 Great job! You\'ve practiced finding locators step by step.');
  });
  
  test('Compare your locators with mine', async ({ page }) => {
    await page.goto('https://www.cricbuzz.com');
    
    console.log('🔄 LOCATOR COMPARISON');
    console.log('=====================');
    console.log('Use the Inspector to find these elements, then compare with my locators:');
    
    // My locators from CricbuzzPage.ts
    const myLocators = {
      logo: '[title*="Cricbuzz"], img[alt*="Cricbuzz"]',
      liveScores: 'a[title*="Live"], a[href*="live"]',
      news: 'a[title*="News"], a[href*="news"]',
      search: 'input[type="search"], input[placeholder*="Search"], #search',
      navigation: '.cb-nav-main, nav, .navigation'
    };
    
    console.log('My locators:');
    Object.entries(myLocators).forEach(([element, locator]) => {
      console.log(`${element}: ${locator}`);
    });
    
    console.log('');
    console.log('Now use the Inspector to find your own locators and compare!');
    await page.pause();
    
    // Test my locators
    console.log('Testing my locators...');
    for (const [element, locator] of Object.entries(myLocators)) {
      try {
        const elementLocator = page.locator(locator);
        const count = await elementLocator.count();
        const isVisible = count > 0 ? await elementLocator.first().isVisible() : false;
        console.log(`${element}: ${count} found, ${isVisible ? 'visible' : 'not visible'}`);
      } catch (error) {
        console.log(`${element}: Error - ${error.message}`);
      }
    }
  });
  
  test('Record actions with Inspector', async ({ page }) => {
    console.log('📹 ACTION RECORDING PRACTICE');
    console.log('============================');
    console.log('This test will let you record a complete user journey:');
    console.log('1. Navigate to different sections');
    console.log('2. Search for something');
    console.log('3. Click on news articles');
    console.log('4. The Inspector will generate code for all your actions!');
    
    await page.goto('https://www.cricbuzz.com');
    await page.pause();
    
    console.log('🎉 Check the Inspector window for the generated code!');
  });
});

// Helper test to demonstrate Inspector features
test('Inspector features demo', async ({ page }) => {
  await page.goto('https://www.cricbuzz.com');
  
  console.log('🛠️ INSPECTOR FEATURES GUIDE');
  console.log('===========================');
  console.log('');
  console.log('Inspector Window Features:');
  console.log('1. 🎯 Pick locator (target icon) - Click to select elements');
  console.log('2. ▶️ Resume - Continue test execution');
  console.log('3. 👆 Step over - Execute next line');
  console.log('4. 📝 Record - Record new actions');
  console.log('5. 🔍 Locator field - Test custom locators');
  console.log('6. 📋 Copy - Copy locator to clipboard');
  console.log('');
  console.log('Browser Features:');
  console.log('1. Blue highlight - Shows selected element');
  console.log('2. Red highlight - Shows all matching elements');
  console.log('3. Console - Shows locator information');
  console.log('');
  console.log('Try each feature now!');
  
  await page.pause();
});
