# Playwright TypeScript Framework for Cricbuzz Automation

This is a comprehensive Playwright automation framework built with TypeScript, specifically configured for testing the Cricbuzz cricket website.

## 🏗️ Framework Structure

```
playwright-ts-framework-advanced/
├── src/
│   ├── pages/           # Page Object Model classes
│   │   ├── HomePage.ts  # Generic homepage (example)
│   │   └── CricbuzzPage.ts  # Cricbuzz-specific page object
│   └── utils/
│       └── TestUtils.ts # Utility functions
├── tests/
│   ├── example.spec.ts      # Basic example test
│   ├── homepage-pom.spec.ts # POM example test
│   └── cricbuzz.spec.ts     # Cricbuzz automation tests
├── test-data/
│   └── test-data.json   # Test data and configurations
├── env/
│   └── dev.json         # Environment-specific settings
├── playwright.config.ts # Playwright configuration
└── package.json         # Dependencies and scripts
```

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### 1. Fix PowerShell Execution Policy (Windows)
Run PowerShell as Administrator and execute:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 2. Install Dependencies
```bash
npm install
npx playwright install
```

### 3. Run Tests
```bash
# Run all tests
npm test

# Run specific test file
npx playwright test cricbuzz.spec.ts

# Run tests in headed mode (see browser)
npx playwright test --headed

# Run tests in specific browser
npx playwright test --project=chromium

# Run tests with debug mode
npx playwright test --debug
```

## 🏏 Cricbuzz Automation Features

### Page Object Model (CricbuzzPage.ts)
The `CricbuzzPage` class provides methods to interact with:

- **Navigation**: Homepage, Live Scores, News, Schedule, Rankings
- **Live Matches**: Get live match data, scores, team information
- **News**: Read headlines, click articles, browse categories
- **Search**: Search for teams, players, tournaments
- **Responsive Testing**: Mobile and tablet viewports

### Key Methods:
```typescript
// Navigation
await cricbuzzPage.navigate();
await cricbuzzPage.clickLiveScores();
await cricbuzzPage.clickNews();

// Data Extraction
const matches = await cricbuzzPage.getLiveMatches();
const headlines = await cricbuzzPage.getNewsHeadlines();

// Interactions
await cricbuzzPage.search('India cricket');
await cricbuzzPage.clickNewsArticle(0);
```

## 📋 Test Scenarios Covered

### Basic Functionality Tests
- ✅ Homepage loading and title verification
- ✅ Logo visibility check
- ✅ Navigation between sections
- ✅ URL validation

### Live Scores Tests
- ✅ Live match data extraction
- ✅ Team names and scores verification
- ✅ Match status checking

### News Tests
- ✅ News headlines retrieval
- ✅ Article navigation
- ✅ News categories verification

### Search Tests
- ✅ Search functionality
- ✅ Search results validation

### Responsive Tests
- ✅ Mobile viewport testing
- ✅ Tablet viewport testing

### Performance Tests
- ✅ Page load time measurement
- ✅ Performance benchmarking

## 🔧 Configuration

### Playwright Config (playwright.config.ts)
- **Timeout**: 60 seconds per test
- **Retries**: 2 retries for failed tests
- **Browsers**: Chrome, Firefox, Safari, Mobile Chrome, Mobile Safari
- **Screenshots**: On failure
- **Videos**: On failure
- **Traces**: On failure

### Environment Config (env/dev.json)
```json
{
  "baseUrl": "https://www.cricbuzz.com",
  "browser": "chromium",
  "headless": false,
  "timeout": 30000,
  "retries": 2
}
```

### Test Data (test-data/test-data.json)
Contains URLs, search terms, and expected values for testing.

## 📊 Reporting

The framework generates multiple report formats:
- **HTML Report**: Visual test results with screenshots
- **JSON Report**: Machine-readable results
- **JUnit Report**: For CI/CD integration

View reports:
```bash
npx playwright show-report
```

## 🛠️ Utility Functions

The `TestUtils` class provides helper methods:
- Network idle waiting
- Screenshot capture
- Random data generation
- Element existence checking
- Retry mechanisms
- Date formatting

## 📱 Mobile Testing

The framework includes mobile testing configurations:
```typescript
// Mobile Chrome (Pixel 5)
// Mobile Safari (iPhone 12)
```

Run mobile tests:
```bash
npx playwright test --project="Mobile Chrome"
npx playwright test --project="Mobile Safari"
```

## 🐛 Debugging

### Debug Mode
```bash
npx playwright test --debug
```

### Headed Mode
```bash
npx playwright test --headed
```

### Specific Test
```bash
npx playwright test cricbuzz.spec.ts --headed --debug
```

## 📈 Best Practices Implemented

1. **Page Object Model**: Separation of test logic and page interactions
2. **Data-Driven Testing**: External test data management
3. **Environment Configuration**: Environment-specific settings
4. **Error Handling**: Robust error handling and retries
5. **Reporting**: Comprehensive test reporting
6. **Cross-Browser Testing**: Multiple browser support
7. **Mobile Testing**: Responsive design validation
8. **Performance Testing**: Load time monitoring

## 🔄 Continuous Integration

The framework is CI/CD ready with:
- JUnit XML reports
- JSON results
- Screenshot and video artifacts
- Configurable headless mode

## 📝 Adding New Tests

1. Create new page objects in `src/pages/`
2. Add test data to `test-data/test-data.json`
3. Write tests in `tests/` directory
4. Use existing utilities from `TestUtils`

Example:
```typescript
import { test, expect } from '@playwright/test';
import { CricbuzzPage } from '../src/pages/CricbuzzPage';

test('my new test', async ({ page }) => {
  const cricbuzzPage = new CricbuzzPage(page);
  await cricbuzzPage.navigate();
  // Your test logic here
});
```

## 🤝 Contributing

1. Follow the existing code structure
2. Add appropriate error handling
3. Include test data in JSON files
4. Write descriptive test names
5. Add comments for complex logic

## 📞 Support

For issues or questions:
1. Check the Playwright documentation
2. Review existing test examples
3. Use debug mode for troubleshooting
4. Check browser console for errors
