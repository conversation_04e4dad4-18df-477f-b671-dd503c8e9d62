{"config": {"configFile": "C:\\Users\\<USER>\\Downloads\\playwright-ts-framework-advanced\\playwright.config.ts", "rootDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"open": "never"}], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/test-results", "repeatEach": 1, "retries": 2, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/test-results", "repeatEach": 1, "retries": 2, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/test-results", "repeatEach": 1, "retries": 2, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/test-results", "repeatEach": 1, "retries": 2, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/test-results", "repeatEach": 1, "retries": 2, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.2", "workers": 1, "webServer": null}, "suites": [], "errors": [{"message": "SyntaxError: C:\\Users\\<USER>\\Downloads\\playwright-ts-framework-advanced\\tests\\locator-practice.spec.ts: Missing semicolon. (148:7)\n\n\u001b[0m \u001b[90m 146 |\u001b[39m   \n \u001b[90m 147 |\u001b[39m   \u001b[90m// Helper method for practicing conditional element finding\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 148 |\u001b[39m   \u001b[36masync\u001b[39m practiceConditionalElements(page) {\n \u001b[90m     |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 149 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'\\n🔄 Practicing conditional element detection:'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 150 |\u001b[39m     \n \u001b[90m 151 |\u001b[39m     \u001b[90m// Elements that might or might not be present\u001b[39m\u001b[0m", "stack": "SyntaxError: C:\\Users\\<USER>\\Downloads\\playwright-ts-framework-advanced\\tests\\locator-practice.spec.ts: Missing semicolon. (148:7)\n\n\u001b[0m \u001b[90m 146 |\u001b[39m   \n \u001b[90m 147 |\u001b[39m   \u001b[90m// Helper method for practicing conditional element finding\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 148 |\u001b[39m   \u001b[36masync\u001b[39m practiceConditionalElements(page) {\n \u001b[90m     |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 149 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'\\n🔄 Practicing conditional element detection:'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 150 |\u001b[39m     \n \u001b[90m 151 |\u001b[39m     \u001b[90m// Elements that might or might not be present\u001b[39m\u001b[0m", "location": {"file": "C:\\Users\\<USER>\\Downloads\\playwright-ts-framework-advanced\\tests\\locator-practice.spec.ts", "line": 148, "column": 7}, "snippet": "\u001b[90m   at \u001b[39mlocator-practice.spec.ts:148\n\n\u001b[0m \u001b[90m 146 |\u001b[39m   \n \u001b[90m 147 |\u001b[39m   \u001b[90m// Helper method for practicing conditional element finding\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 148 |\u001b[39m   \u001b[36masync\u001b[39m practiceConditionalElements(page) {\n \u001b[90m     |\u001b[39m       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 149 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'\\n🔄 Practicing conditional element detection:'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 150 |\u001b[39m     \n \u001b[90m 151 |\u001b[39m     \u001b[90m// Elements that might or might not be present\u001b[39m\u001b[0m"}], "stats": {"startTime": "2025-08-14T05:34:28.183Z", "duration": 451.7059999999997, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}