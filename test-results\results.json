{"config": {"configFile": "C:\\Users\\<USER>\\Downloads\\playwright-ts-framework-advanced\\playwright.config.ts", "rootDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"open": "never"}], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/test-results", "repeatEach": 1, "retries": 2, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/test-results", "repeatEach": 1, "retries": 2, "metadata": {"actualWorkers": 1}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/test-results", "repeatEach": 1, "retries": 2, "metadata": {"actualWorkers": 1}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/test-results", "repeatEach": 1, "retries": 2, "metadata": {"actualWorkers": 1}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/test-results", "repeatEach": 1, "retries": 2, "metadata": {"actualWorkers": 1}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/Downloads/playwright-ts-framework-advanced/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.2", "workers": 1, "webServer": null}, "suites": [{"title": "example.spec.ts", "file": "example.spec.ts", "column": 0, "line": 0, "specs": [{"title": "homepage has title", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 6631, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-14T05:36:46.437Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "a30a6eba6312f6b87ea5-96e03ec3ded4d2e1b6e1", "file": "example.spec.ts", "line": 4, "column": 5}, {"title": "homepage has title", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 1, "parallelIndex": 0, "status": "passed", "duration": 32057, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-14T05:36:59.402Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "a30a6eba6312f6b87ea5-7bc04e79cb0eca3e1634", "file": "example.spec.ts", "line": 4, "column": 5}, {"title": "homepage has title", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 2, "parallelIndex": 0, "status": "passed", "duration": 2952, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-14T05:37:38.059Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "a30a6eba6312f6b87ea5-3c32625c1415f6ca920d", "file": "example.spec.ts", "line": 4, "column": 5}, {"title": "homepage has title", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 3, "parallelIndex": 0, "status": "passed", "duration": 2411, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-14T05:37:44.986Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "a30a6eba6312f6b87ea5-8e579a2253d2a7b70200", "file": "example.spec.ts", "line": 4, "column": 5}, {"title": "homepage has title", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "passed", "duration": 3850, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-14T05:37:51.489Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "a30a6eba6312f6b87ea5-4fb47fd8619ed872f319", "file": "example.spec.ts", "line": 4, "column": 5}]}], "errors": [], "stats": {"startTime": "2025-08-14T05:36:44.176Z", "duration": 71429.973, "expected": 5, "skipped": 0, "unexpected": 0, "flaky": 0}}