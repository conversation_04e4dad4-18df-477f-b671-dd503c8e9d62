import { test, expect } from '@playwright/test';

test.describe('Locator Discovery Practice', () => {
  
  test('Practice finding locators on Cricbuzz', async ({ page }) => {
    // Navigate to Cricbuzz
    await page.goto('https://www.cricbuzz.com');
    
    // EXERCISE 1: Find the main logo
    // Try different approaches:
    
    // Method 1: By title attribute
    const logoByTitle = page.locator('[title*="Cricbuzz"]');
    await expect(logoByTitle).toBeVisible();
    console.log('✅ Found logo by title attribute');
    
    // Method 2: By alt text
    const logoByAlt = page.locator('img[alt*="Cricbuzz"]');
    await expect(logoByAlt).toBeVisible();
    console.log('✅ Found logo by alt attribute');
    
    // EXERCISE 2: Find navigation menu items
    // Look for different ways to locate the same element
    
    // Method 1: By text content
    const liveScoresByText = page.getByText('Live Scores');
    if (await liveScoresByText.isVisible()) {
      console.log('✅ Found Live Scores by text');
    }
    
    // Method 2: By href attribute
    const liveScoresByHref = page.locator('a[href*="live"]');
    if (await liveScoresByHref.first().isVisible()) {
      console.log('✅ Found Live Scores by href');
    }
    
    // Method 3: By title attribute
    const liveScoresByTitle = page.locator('a[title*="Live"]');
    if (await liveScoresByTitle.first().isVisible()) {
      console.log('✅ Found Live Scores by title');
    }
    
    // EXERCISE 3: Practice with different selector types
    await this.practiceSelectors(page);
    
    // EXERCISE 4: Find elements that might not exist
    await this.practiceConditionalElements(page);
  });
  
  test('Selector strategy comparison', async ({ page }) => {
    await page.goto('https://www.cricbuzz.com');
    
    // Compare different ways to find the same element
    const strategies = [
      { name: 'CSS Class', selector: '.cb-nav-main' },
      { name: 'Tag + Class', selector: 'nav.cb-nav-main' },
      { name: 'Descendant', selector: 'header nav' },
      { name: 'Attribute', selector: '[class*="nav-main"]' }
    ];
    
    for (const strategy of strategies) {
      try {
        const element = page.locator(strategy.selector);
        const isVisible = await element.isVisible();
        console.log(`${strategy.name} (${strategy.selector}): ${isVisible ? '✅ Found' : '❌ Not found'}`);
      } catch (error) {
        console.log(`${strategy.name} (${strategy.selector}): ❌ Error - ${error.message}`);
      }
    }
  });
  
  test('Interactive locator discovery', async ({ page }) => {
    await page.goto('https://www.cricbuzz.com');
    
    // This test will pause so you can manually inspect elements
    console.log('🔍 MANUAL INSPECTION TIME!');
    console.log('1. Right-click on different elements');
    console.log('2. Select "Inspect Element"');
    console.log('3. Look at the HTML structure');
    console.log('4. Try different selector strategies');
    console.log('5. Test them in the browser console');
    
    // Uncomment the next line to pause for manual inspection
    // await page.pause();
    
    // After manual inspection, test some common elements
    const elementsToFind = [
      'Search box',
      'Live Scores link', 
      'News section',
      'Match cards',
      'Footer links'
    ];
    
    console.log('\n📝 Elements to practice finding:');
    elementsToFind.forEach((element, index) => {
      console.log(`${index + 1}. ${element}`);
    });
  });
  
  // Helper method for practicing different selectors
  async function practiceSelectors(page: any) {
    console.log('\n🎯 Practicing different selector types:');
    
    // ID selectors (if any exist)
    try {
      const elementById = page.locator('#search-input, #search-box, #main-search');
      if (await elementById.first().isVisible()) {
        console.log('✅ Found element by ID');
      }
    } catch {
      console.log('❌ No elements found by common ID patterns');
    }
    
    // Class selectors
    const commonClasses = ['.cb-nav-main', '.cb-mtch-lst', '.cb-nws-hdln'];
    for (const className of commonClasses) {
      try {
        const element = page.locator(className);
        if (await element.first().isVisible()) {
          console.log(`✅ Found element by class: ${className}`);
        }
      } catch {
        console.log(`❌ Class not found: ${className}`);
      }
    }
    
    // Attribute selectors
    const attributeSelectors = [
      '[title*="Cricket"]',
      '[href*="live"]',
      '[class*="nav"]'
    ];
    
    for (const selector of attributeSelectors) {
      try {
        const element = page.locator(selector);
        if (await element.first().isVisible()) {
          console.log(`✅ Found element by attribute: ${selector}`);
        }
      } catch {
        console.log(`❌ Attribute selector failed: ${selector}`);
      }
    }
  }
  
  // Helper method for practicing conditional element finding
  async function practiceConditionalElements(page: any) {
    console.log('\n🔄 Practicing conditional element detection:');
    
    // Elements that might or might not be present
    const conditionalElements = [
      { name: 'Live match banner', selector: '.cb-live-banner, .live-match-banner' },
      { name: 'Advertisement', selector: '.ad-container, .advertisement' },
      { name: 'Breaking news', selector: '.breaking-news, .cb-breaking' },
      { name: 'Login button', selector: '.login-btn, .sign-in' }
    ];
    
    for (const element of conditionalElements) {
      try {
        const locator = page.locator(element.selector);
        const count = await locator.count();
        const isVisible = count > 0 ? await locator.first().isVisible() : false;
        
        console.log(`${element.name}: ${count} found, ${isVisible ? 'visible' : 'not visible'}`);
      } catch (error) {
        console.log(`${element.name}: Error - ${error.message}`);
      }
    }
  }
});
