// Playwright Locator Discovery Script
// Run this to explore Cricbuzz and find locators interactively

const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // Navigate to Cricbuzz
  await page.goto('https://www.cricbuzz.com');
  
  // Pause for manual inspection
  // This will open Playwright Inspector where you can:
  // 1. Click on elements to see their locators
  // 2. Test different selector strategies
  // 3. Record actions automatically
  await page.pause();
  
  await browser.close();
})();
